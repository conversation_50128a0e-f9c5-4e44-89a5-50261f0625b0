<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="8dp">

    <FrameLayout
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:background="@android:color/darker_gray">

        <ImageView
            android:id="@+id/filter_preview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop" />

        <!-- 选中状态的边框 -->
        <View
            android:id="@+id/selection_border"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/filter_selection_border"
            android:visibility="gone" />

    </FrameLayout>

    <TextView
        android:id="@+id/filter_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:gravity="center"
        android:maxLines="1"
        android:text="滤镜名称"
        android:textColor="@android:color/white"
        android:textSize="12sp" />

</LinearLayout>
