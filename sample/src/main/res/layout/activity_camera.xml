<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!--<android.opengl.GLSurfaceView-->
    <!--android:id="@+id/surfaceView"-->
    <!--android:layout_width="match_parent"-->
    <!--android:layout_height="match_parent"-->
    <!--android:layout_above="@+id/bar" />-->

    <!--<jp.co.cyberagent.android.gpuimage.GLTextureView-->
    <!--android:id="@+id/surfaceView"-->
    <!--android:layout_width="match_parent"-->
    <!--android:layout_height="match_parent"-->
    <!--android:layout_above="@+id/bar" />-->

    <jp.co.cyberagent.android.gpuimage.GPUImageView
        android:id="@+id/surfaceView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:gpuimage_show_loading="false"
        app:gpuimage_surface_type="texture_view" /> <!-- surface_view or texture_view -->

    <ImageView
        android:id="@+id/img_switch_camera"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:contentDescription="@null"
        android:padding="10dp"
        android:src="@drawable/ic_switch_camera"
        tools:ignore="HardcodedText,RtlHardcoded" />

    <LinearLayout
        android:id="@+id/bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="#000000"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <SeekBar
            android:id="@+id/seekBar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:max="100" />

        <Button
            android:id="@+id/button_choose_filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Choose Filter"
            tools:ignore="HardcodedText" />

        <ImageButton
            android:id="@+id/button_capture"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@null"
            android:src="@android:drawable/ic_menu_camera" />
    </LinearLayout>

</RelativeLayout>