<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    tools:context=".activity.LightroomEditorActivity">

    <!-- 顶部工具栏 -->
    <LinearLayout
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="#1a1a1a"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回"
            android:src="@android:drawable/ic_menu_revert"
            android:tint="@android:color/white" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="滤镜编辑器"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageButton
            android:id="@+id/btn_save"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="保存"
            android:src="@android:drawable/ic_menu_save"
            android:tint="@android:color/white" />

    </LinearLayout>

    <!-- 图片预览区域 -->
    <FrameLayout
        android:id="@+id/preview_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="8dp"
        app:layout_constraintBottom_toTopOf="@+id/control_panel"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <jp.co.cyberagent.android.gpuimage.GPUImageView
            android:id="@+id/gpu_image_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            app:gpuimage_show_loading="false"
            app:gpuimage_surface_type="texture_view" />

    </FrameLayout>

    <!-- 底部控制面板 -->
    <LinearLayout
        android:id="@+id/control_panel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#1a1a1a"
        android:orientation="vertical"
        android:paddingBottom="16dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- 参数调整区域 -->
        <LinearLayout
            android:id="@+id/adjustment_panel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="16dp"
            android:paddingTop="16dp"
            android:visibility="gone">

            <!-- 亮度调整 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="亮度"
                    android:textColor="@android:color/white"
                    android:textSize="14sp" />

                <SeekBar
                    android:id="@+id/seekbar_brightness"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="50" />

                <TextView
                    android:id="@+id/tv_brightness_value"
                    android:layout_width="40dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="0.00"
                    android:textColor="@android:color/white"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- 对比度调整 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="对比度"
                    android:textColor="@android:color/white"
                    android:textSize="14sp" />

                <SeekBar
                    android:id="@+id/seekbar_contrast"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="50" />

                <TextView
                    android:id="@+id/tv_contrast_value"
                    android:layout_width="40dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="0"
                    android:textColor="@android:color/white"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- 饱和度调整 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="饱和度"
                    android:textColor="@android:color/white"
                    android:textSize="14sp" />

                <SeekBar
                    android:id="@+id/seekbar_saturation"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="50" />

                <TextView
                    android:id="@+id/tv_saturation_value"
                    android:layout_width="40dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="0"
                    android:textColor="@android:color/white"
                    android:textSize="12sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 滤镜选择区域 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/filter_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:paddingHorizontal="8dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        <!-- 底部功能按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <Button
                android:id="@+id/btn_adjust"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:background="@android:color/transparent"
                android:text="调节"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

            <Button
                android:id="@+id/btn_effects"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:background="@android:color/transparent"
                android:text="效果"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
