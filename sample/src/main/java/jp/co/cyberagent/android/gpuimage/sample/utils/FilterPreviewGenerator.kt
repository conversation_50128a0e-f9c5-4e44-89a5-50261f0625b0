package jp.co.cyberagent.android.gpuimage.sample.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import jp.co.cyberagent.android.gpuimage.GPUImage
import jp.co.cyberagent.android.gpuimage.GPUImageFilter
import jp.co.cyberagent.android.gpuimage.sample.R

object FilterPreviewGenerator {
    
    private var sampleBitmap: Bitmap? = null
    
    fun generatePreview(context: Context, filter: GPUImageFilter): Bitmap? {
        return try {
            // 获取示例图片
            val bitmap = getSampleBitmap(context)
            
            // 创建GPUImage实例
            val gpuImage = GPUImage(context)
            gpuImage.setFilter(filter)
            
            // 生成预览图片
            val previewBitmap = gpuImage.getBitmapWithFilterApplied(bitmap)
            
            // 缩放到合适的预览尺寸
            Bitmap.createScaledBitmap(previewBitmap, 80, 80, true)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    private fun getSampleBitmap(context: Context): Bitmap {
        if (sampleBitmap == null) {
            // 使用应用图标作为示例图片，或者你可以添加一个专门的示例图片
            sampleBitmap = BitmapFactory.decodeResource(context.resources, R.drawable.ic_launcher)
        }
        return sampleBitmap!!
    }
    
    fun generatePreviewsAsync(
        context: Context, 
        filters: List<GPUImageFilter>,
        callback: (Int, Bitmap?) -> Unit
    ) {
        Thread {
            filters.forEachIndexed { index, filter ->
                val preview = generatePreview(context, filter)
                callback(index, preview)
            }
        }.start()
    }
}
