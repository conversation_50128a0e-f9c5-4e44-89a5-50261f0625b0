package jp.co.cyberagent.android.gpuimage.sample.adapter

import android.content.Context
import android.graphics.Bitmap
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import jp.co.cyberagent.android.gpuimage.GPUImageFilter
import jp.co.cyberagent.android.gpuimage.sample.R

data class FilterItem(
    val name: String,
    val filter: GPUImageFilter,
    var previewBitmap: Bitmap? = null
)

class FilterAdapter(
    private val context: Context,
    private val filters: MutableList<FilterItem>,
    private val onFilterSelected: (FilterItem) -> Unit
) : RecyclerView.Adapter<FilterAdapter.FilterViewHolder>() {

    private var selectedPosition = 0

    inner class FilterViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val filterPreview: ImageView = itemView.findViewById(R.id.filter_preview)
        val filterName: TextView = itemView.findViewById(R.id.filter_name)
        val selectionBorder: View = itemView.findViewById(R.id.selection_border)

        init {
            itemView.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    val previousPosition = selectedPosition
                    selectedPosition = position
                    
                    // 更新选中状态
                    notifyItemChanged(previousPosition)
                    notifyItemChanged(selectedPosition)
                    
                    // 回调选中的滤镜
                    onFilterSelected(filters[position])
                }
            }
        }

        fun bind(filterItem: FilterItem, isSelected: Boolean) {
            filterName.text = filterItem.name
            
            // 设置预览图片
            if (filterItem.previewBitmap != null) {
                filterPreview.setImageBitmap(filterItem.previewBitmap)
            } else {
                // 如果没有预览图，显示默认图片
                filterPreview.setImageResource(R.drawable.ic_launcher)
            }
            
            // 设置选中状态
            selectionBorder.visibility = if (isSelected) View.VISIBLE else View.GONE
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FilterViewHolder {
        val view = LayoutInflater.from(context).inflate(R.layout.item_filter, parent, false)
        return FilterViewHolder(view)
    }

    override fun onBindViewHolder(holder: FilterViewHolder, position: Int) {
        holder.bind(filters[position], position == selectedPosition)
    }

    override fun getItemCount(): Int = filters.size

    fun updatePreview(position: Int, bitmap: Bitmap?) {
        if (position in 0 until filters.size) {
            filters[position].previewBitmap = bitmap
            notifyItemChanged(position)
        }
    }

    fun getSelectedFilter(): FilterItem? {
        return if (selectedPosition in 0 until filters.size) {
            filters[selectedPosition]
        } else null
    }
}
