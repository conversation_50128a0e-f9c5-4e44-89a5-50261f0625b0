package jp.co.cyberagent.android.gpuimage.sample.activity

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.widget.*
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import jp.co.cyberagent.android.gpuimage.GPUImage
import jp.co.cyberagent.android.gpuimage.GPUImageView
import jp.co.cyberagent.android.gpuimage.filter.*
import jp.co.cyberagent.android.gpuimage.sample.R
import jp.co.cyberagent.android.gpuimage.sample.adapter.FilterAdapter
import jp.co.cyberagent.android.gpuimage.sample.adapter.FilterItem
import jp.co.cyberagent.android.gpuimage.sample.utils.FilterPreviewGenerator
import java.io.InputStream

class LightroomEditorActivity : AppCompatActivity() {

    private lateinit var gpuImageView: GPUImageView
    private lateinit var filterRecyclerView: RecyclerView
    private lateinit var adjustmentPanel: LinearLayout
    private lateinit var btnAdjust: Button
    private lateinit var btnEffects: Button
    
    // 参数调整控件
    private lateinit var seekbarBrightness: SeekBar
    private lateinit var seekbarContrast: SeekBar
    private lateinit var seekbarSaturation: SeekBar
    private lateinit var tvBrightnessValue: TextView
    private lateinit var tvContrastValue: TextView
    private lateinit var tvSaturationValue: TextView

    private lateinit var filterAdapter: FilterAdapter
    private var originalBitmap: Bitmap? = null
    private var currentImageUri: Uri? = null

    // 当前的滤镜组合
    private var brightnessFilter = GPUImageBrightnessFilter(0.0f)
    private var contrastFilter = GPUImageContrastFilter(1.0f)
    private var saturationFilter = GPUImageSaturationFilter(1.0f)
    private var effectFilter: GPUImageFilter? = null

    // 图片选择器
    private val imagePickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { activityResult ->
        if (activityResult.resultCode == Activity.RESULT_OK) {
            activityResult.data?.data?.let { uri ->
                currentImageUri = uri
                loadImage(uri)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_lightroom_editor)

        initViews()
        setupFilterList()
        setupSeekBars()
        setupButtons()
        
        // 获取传入的图片URI，如果没有则启动图片选择器
        currentImageUri = intent.getParcelableExtra("image_uri")
        if (currentImageUri != null) {
            loadImage(currentImageUri!!)
        } else {
            startImagePicker()
        }
    }

    private fun initViews() {
        gpuImageView = findViewById(R.id.gpu_image_view)
        filterRecyclerView = findViewById(R.id.filter_recycler_view)
        adjustmentPanel = findViewById(R.id.adjustment_panel)
        btnAdjust = findViewById(R.id.btn_adjust)
        btnEffects = findViewById(R.id.btn_effects)
        
        seekbarBrightness = findViewById(R.id.seekbar_brightness)
        seekbarContrast = findViewById(R.id.seekbar_contrast)
        seekbarSaturation = findViewById(R.id.seekbar_saturation)
        tvBrightnessValue = findViewById(R.id.tv_brightness_value)
        tvContrastValue = findViewById(R.id.tv_contrast_value)
        tvSaturationValue = findViewById(R.id.tv_saturation_value)

        findViewById<ImageButton>(R.id.btn_back).setOnClickListener { finish() }
        findViewById<ImageButton>(R.id.btn_save).setOnClickListener { saveImage() }
    }

    private fun setupFilterList() {
        val filters = createFilterList()
        filterAdapter = FilterAdapter(this, filters) { filterItem ->
            applyEffectFilter(filterItem.filter)
        }
        
        filterRecyclerView.layoutManager = LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        filterRecyclerView.adapter = filterAdapter
    }

    private fun createFilterList(): MutableList<FilterItem> {
        val filters = mutableListOf(
            FilterItem("原图", GPUImageFilter()),
            FilterItem("黑白", GPUImageGrayscaleFilter()),
            FilterItem("复古", GPUImageSepiaToneFilter()),
            FilterItem("反色", GPUImageColorInvertFilter()),
            FilterItem("锐化", GPUImageSharpenFilter()),
            FilterItem("模糊", GPUImageGaussianBlurFilter()),
            FilterItem("素描", GPUImageSketchFilter()),
            FilterItem("卡通", GPUImageToonFilter()),
            FilterItem("浮雕", GPUImageEmbossFilter()),
            FilterItem("像素化", GPUImagePixelationFilter())
        )

        // 异步生成预览图片
        generateFilterPreviews(filters)

        return filters
    }

    private fun setupSeekBars() {
        // 亮度调整
        seekbarBrightness.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val brightness = (progress - 50) / 50.0f // -1.0 to 1.0
                    brightnessFilter.setBrightness(brightness)
                    tvBrightnessValue.text = String.format("%.2f", brightness)
                    updateFilters()
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 对比度调整
        seekbarContrast.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val contrast = progress / 50.0f // 0.0 to 2.0
                    contrastFilter.setContrast(contrast)
                    tvContrastValue.text = String.format("%.2f", contrast)
                    updateFilters()
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 饱和度调整
        seekbarSaturation.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val saturation = progress / 50.0f // 0.0 to 2.0
                    saturationFilter.setSaturation(saturation)
                    tvSaturationValue.text = String.format("%.2f", saturation)
                    updateFilters()
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
    }

    private fun setupButtons() {
        btnAdjust.setOnClickListener {
            adjustmentPanel.visibility = View.VISIBLE
            filterRecyclerView.visibility = View.GONE
            updateButtonStates(true)
        }

        btnEffects.setOnClickListener {
            adjustmentPanel.visibility = View.GONE
            filterRecyclerView.visibility = View.VISIBLE
            updateButtonStates(false)
        }
        
        // 默认显示效果面板
        btnEffects.performClick()
    }

    private fun updateButtonStates(adjustMode: Boolean) {
        if (adjustMode) {
            btnAdjust.setBackgroundColor(resources.getColor(android.R.color.holo_blue_dark))
            btnEffects.setBackgroundColor(resources.getColor(android.R.color.transparent))
        } else {
            btnAdjust.setBackgroundColor(resources.getColor(android.R.color.transparent))
            btnEffects.setBackgroundColor(resources.getColor(android.R.color.holo_blue_dark))
        }
    }

    private fun loadImage(uri: Uri) {
        try {
            val inputStream: InputStream? = contentResolver.openInputStream(uri)
            originalBitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()
            
            originalBitmap?.let { bitmap ->
                gpuImageView.setImage(bitmap)
                updateFilters()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "加载图片失败", Toast.LENGTH_SHORT).show()
        }
    }

    private fun applyEffectFilter(filter: GPUImageFilter) {
        effectFilter = filter
        updateFilters()
    }

    private fun updateFilters() {
        val filterGroup = GPUImageFilterGroup()
        val filters = mutableListOf<GPUImageFilter>()
        
        // 添加基础调整滤镜
        filters.add(brightnessFilter)
        filters.add(contrastFilter)
        filters.add(saturationFilter)
        
        // 添加效果滤镜
        effectFilter?.let { filters.add(it) }
        
        filterGroup.filters = filters
        gpuImageView.filter = filterGroup
        gpuImageView.requestRender()
    }

    private fun saveImage() {
        try {
            val folderName = "GPUImage"
            val fileName = "lightroom_edit_${System.currentTimeMillis()}.jpg"
            gpuImageView.saveToPictures(folderName, fileName) {
                Toast.makeText(this, "图片已保存到 $folderName/$fileName", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "保存失败", Toast.LENGTH_SHORT).show()
        }
    }

    private fun generateFilterPreviews(filters: MutableList<FilterItem>) {
        FilterPreviewGenerator.generatePreviewsAsync(
            this,
            filters.map { it.filter }
        ) { index, bitmap ->
            runOnUiThread {
                filterAdapter.updatePreview(index, bitmap)
            }
        }
    }

    private fun startImagePicker() {
        val intent = Intent(Intent.ACTION_PICK).apply {
            type = "image/*"
        }
        imagePickerLauncher.launch(intent)
    }

    companion object {
        const val EXTRA_IMAGE_URI = "image_uri"
    }
}
