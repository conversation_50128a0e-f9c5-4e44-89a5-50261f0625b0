1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="jp.co.cyberagent.android.gpuimage.sample"
4    android:versionCode="14"
5    android:versionName="2.1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="14"
8-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
9        android:targetSdkVersion="30" />
9-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
11-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:5:5-81
11-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:5:22-78
12    <uses-permission android:name="android.permission.CAMERA" />
12-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:6:5-65
12-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:6:22-62
13
14    <!-- For images from picasa -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:9:5-67
15-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:9:22-64
16
17    <uses-feature android:name="android.hardware.camera" />
17-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:11:5-60
17-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:11:19-57
18    <uses-feature
18-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:12:5-14:36
19        android:name="android.hardware.camera.autofocus"
19-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:13:9-57
20        android:required="false" />
20-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:14:9-33
21
22    <application
22-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:16:5-41:19
23        android:allowBackup="false"
23-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:17:9-36
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.3.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/22e35acf320ba1436f8703b3b8ba64f7/core-1.3.0/AndroidManifest.xml:24:18-86
25        android:debuggable="true"
26        android:hardwareAccelerated="true"
26-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:18:9-43
27        android:icon="@drawable/ic_launcher"
27-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:19:9-45
28        android:label="@string/app_name"
28-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:20:9-41
29        android:largeHeap="true"
29-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:21:9-33
30        android:theme="@style/AppTheme" >
30-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:22:9-40
31        <activity
31-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:24:9-32:20
32            android:name="jp.co.cyberagent.android.gpuimage.sample.activity.MainActivity"
32-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:25:13-50
33            android:label="@string/title_activity_activity_main" >
33-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:26:13-65
34            <intent-filter>
34-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:27:13-31:29
35                <action android:name="android.intent.action.MAIN" />
35-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:28:17-69
35-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:28:25-66
36
37                <category android:name="android.intent.category.LAUNCHER" />
37-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:30:17-77
37-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:30:27-74
38            </intent-filter>
39        </activity>
40        <activity android:name="jp.co.cyberagent.android.gpuimage.sample.activity.GalleryActivity" />
40-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:33:9-62
40-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:33:19-59
41        <activity
41-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:34:9-37:59
42            android:name="jp.co.cyberagent.android.gpuimage.sample.activity.CameraActivity"
42-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:35:13-52
43            android:screenOrientation="portrait"
43-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:36:13-49
44            android:theme="@style/AppTheme.NoActionBar" />
44-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:37:13-56
45        <activity
45-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:38:9-40:59
46            android:name="jp.co.cyberagent.android.gpuimage.sample.activity.LightroomEditorActivity"
46-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:39:13-61
47            android:theme="@style/AppTheme.NoActionBar" />
47-->/Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:40:13-56
48    </application>
49
50</manifest>
