jp.co.cyberagent.android.gpuimage.sample:styleable/ViewStubCompat = 0x7f0e002a
jp.co.cyberagent.android.gpuimage.sample:styleable/Toolbar = 0x7f0e0027
jp.co.cyberagent.android.gpuimage.sample:styleable/RecycleListView = 0x7f0e0020
jp.co.cyberagent.android.gpuimage.sample:styleable/PopupWindow = 0x7f0e001e
jp.co.cyberagent.android.gpuimage.sample:styleable/MenuView = 0x7f0e001d
jp.co.cyberagent.android.gpuimage.sample:styleable/MenuItem = 0x7f0e001c
jp.co.cyberagent.android.gpuimage.sample:styleable/LinearLayoutCompat = 0x7f0e0018
jp.co.cyberagent.android.gpuimage.sample:styleable/CompoundButton = 0x7f0e0011
jp.co.cyberagent.android.gpuimage.sample:styleable/ColorStateListItem = 0x7f0e0010
jp.co.cyberagent.android.gpuimage.sample:styleable/AppCompatImageView = 0x7f0e000a
jp.co.cyberagent.android.gpuimage.sample:styleable/AnimatedStateListDrawableItem = 0x7f0e0008
jp.co.cyberagent.android.gpuimage.sample:styleable/ActivityChooserView = 0x7f0e0005
jp.co.cyberagent.android.gpuimage.sample:styleable/ActionBar = 0x7f0e0000
jp.co.cyberagent.android.gpuimage.sample:style/Widget.Compat.NotificationActionContainer = 0x7f0d015e
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f0d015b
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.TextView = 0x7f0d015a
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Spinner.Underlined = 0x7f0d0159
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f0d0158
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Spinner.DropDown = 0x7f0d0157
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.SearchView = 0x7f0d0152
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.RatingBar = 0x7f0d014f
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f0d014b
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.PopupMenu = 0x7f0d014a
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ListView = 0x7f0d0147
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ListPopupWindow = 0x7f0d0146
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f0d0144
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0d0142
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f0d0140
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f0d013e
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f0d013b
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f0d013a
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0d0135
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0d0132
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0d0127
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ButtonBar = 0x7f0d0126
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Button.Small = 0x7f0d0125
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Button.Colored = 0x7f0d0124
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f0d0122
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ActivityChooserView = 0x7f0d011e
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ActionMode = 0x7f0d011d
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ActionButton.Overflow = 0x7f0d011c
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f0d011b
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ActionButton = 0x7f0d011a
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ActionBar.TabText = 0x7f0d0118
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ActionBar = 0x7f0d0115
jp.co.cyberagent.android.gpuimage.sample:style/ThemeOverlay.AppCompat.Light = 0x7f0d0114
jp.co.cyberagent.android.gpuimage.sample:style/ThemeOverlay.AppCompat.Dialog = 0x7f0d0112
jp.co.cyberagent.android.gpuimage.sample:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f0d0111
jp.co.cyberagent.android.gpuimage.sample:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0d010f
jp.co.cyberagent.android.gpuimage.sample:style/ThemeOverlay.AppCompat.Dark = 0x7f0d010e
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.Light.NoActionBar = 0x7f0d010a
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f0d0109
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0d0108
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f0d0107
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.Light.Dialog = 0x7f0d0106
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.DialogWhenLarge = 0x7f0d0102
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.Dialog.Alert = 0x7f0d0100
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.Dialog = 0x7f0d00ff
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f0d00fd
jp.co.cyberagent.android.gpuimage.sample:styleable/AlertDialog = 0x7f0e0006
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f0d00fb
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.DayNight.Dialog = 0x7f0d00fa
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.DayNight = 0x7f0d00f8
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.CompactMenu = 0x7f0d00f7
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat = 0x7f0d00f6
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.Compat.Notification.Time = 0x7f0d00f1
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0d00ed
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.Light = 0x7f0d0104
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.Switch = 0x7f0d00ec
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0d00ea
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0d00e9
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0d00e5
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.Button = 0x7f0d00e4
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.Empty = 0x7f0d0103
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f0d00e3
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0d00e2
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0d00e0
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0d00df
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0d00de
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0d00dc
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0d00db
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Title = 0x7f0d00d8
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Subhead = 0x7f0d00d6
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.SeekBar = 0x7f0d0154
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Small = 0x7f0d00d4
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f0d00d3
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0d00d2
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f0d00d0
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Medium = 0x7f0d00cf
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0d00ce
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0d00cd
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Large.Inverse = 0x7f0d00ca
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Inverse = 0x7f0d00c8
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Display3 = 0x7f0d00c5
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0d00e8
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Display2 = 0x7f0d00c4
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Display1 = 0x7f0d00c3
jp.co.cyberagent.android.gpuimage.sample:styleable/PopupWindowBackgroundState = 0x7f0e001f
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Button = 0x7f0d00c1
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat = 0x7f0d00be
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.AutoCompleteTextView = 0x7f0d011f
jp.co.cyberagent.android.gpuimage.sample:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f0d00bb
jp.co.cyberagent.android.gpuimage.sample:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f0d00b7
jp.co.cyberagent.android.gpuimage.sample:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f0d00b6
jp.co.cyberagent.android.gpuimage.sample:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f0d00b5
jp.co.cyberagent.android.gpuimage.sample:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f0d00b4
jp.co.cyberagent.android.gpuimage.sample:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f0d00b3
jp.co.cyberagent.android.gpuimage.sample:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f0d00b1
jp.co.cyberagent.android.gpuimage.sample:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f0d00ae
jp.co.cyberagent.android.gpuimage.sample:style/Platform.V21.AppCompat.Light = 0x7f0d00a9
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f0d013c
jp.co.cyberagent.android.gpuimage.sample:style/Platform.V21.AppCompat = 0x7f0d00a8
jp.co.cyberagent.android.gpuimage.sample:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f0d00a6
jp.co.cyberagent.android.gpuimage.sample:style/Platform.AppCompat = 0x7f0d00a3
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0d00a2
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Toolbar = 0x7f0d00a1
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f0d009e
jp.co.cyberagent.android.gpuimage.sample:styleable/MenuGroup = 0x7f0e001b
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.SeekBar = 0x7f0d009b
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f0d009a
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.SearchView = 0x7f0d0099
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f0d0097
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f0d0095
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.PopupWindow = 0x7f0d0093
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f0d0092
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.ActionButton = 0x7f0d0138
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ListView = 0x7f0d008e
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ListMenuView = 0x7f0d008c
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f0d0087
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f0d0084
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f0d0081
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f0d007c
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Button.Small = 0x7f0d0079
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Button.Colored = 0x7f0d0078
jp.co.cyberagent.android.gpuimage.sample:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f0d00a7
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Button.Borderless = 0x7f0d0075
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f0d0073
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ActionMode = 0x7f0d0071
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f0d0070
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ActionButton = 0x7f0d006e
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f0d006d
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f0d006a
jp.co.cyberagent.android.gpuimage.sample:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f0d0066
jp.co.cyberagent.android.gpuimage.sample:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f0d0065
jp.co.cyberagent.android.gpuimage.sample:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f0d0064
jp.co.cyberagent.android.gpuimage.sample:style/Base.V7.Theme.AppCompat.Light = 0x7f0d0063
jp.co.cyberagent.android.gpuimage.sample:style/Base.V7.Theme.AppCompat.Dialog = 0x7f0d0062
jp.co.cyberagent.android.gpuimage.sample:style/Base.V28.Theme.AppCompat.Light = 0x7f0d0060
jp.co.cyberagent.android.gpuimage.sample:style/Base.V28.Theme.AppCompat = 0x7f0d005f
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Tooltip = 0x7f0d00da
jp.co.cyberagent.android.gpuimage.sample:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f0d005e
jp.co.cyberagent.android.gpuimage.sample:style/Base.V22.Theme.AppCompat.Light = 0x7f0d0059
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.RatingBar.Indicator = 0x7f0d0150
jp.co.cyberagent.android.gpuimage.sample:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f0d0056
jp.co.cyberagent.android.gpuimage.sample:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f0d00ba
jp.co.cyberagent.android.gpuimage.sample:style/Base.V21.Theme.AppCompat.Dialog = 0x7f0d0054
jp.co.cyberagent.android.gpuimage.sample:style/Base.V21.Theme.AppCompat = 0x7f0d0053
jp.co.cyberagent.android.gpuimage.sample:style/Base.ThemeOverlay.AppCompat.Light = 0x7f0d0052
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.EditText = 0x7f0d0082
jp.co.cyberagent.android.gpuimage.sample:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0d0051
jp.co.cyberagent.android.gpuimage.sample:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0d004f
jp.co.cyberagent.android.gpuimage.sample:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f0d004b
jp.co.cyberagent.android.gpuimage.sample:style/Base.Theme.AppCompat.Light.Dialog = 0x7f0d0047
jp.co.cyberagent.android.gpuimage.sample:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f0d0042
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.EditText = 0x7f0d012d
jp.co.cyberagent.android.gpuimage.sample:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f0d0041
jp.co.cyberagent.android.gpuimage.sample:style/Base.Theme.AppCompat.Dialog = 0x7f0d0040
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0d003d
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0d003c
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0d003a
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Spinner = 0x7f0d0156
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f0d0039
jp.co.cyberagent.android.gpuimage.sample:style/Base.V23.Theme.AppCompat.Light = 0x7f0d005b
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0d0036
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0d0037
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0d0035
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0d0034
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0d0033
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0d0032
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f0d0028
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Subhead = 0x7f0d0025
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f0d0022
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f0d0020
jp.co.cyberagent.android.gpuimage.sample:style/Base.Theme.AppCompat.CompactMenu = 0x7f0d003f
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Menu = 0x7f0d001f
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Medium = 0x7f0d001d
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f0d001a
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Inverse = 0x7f0d0018
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Display4 = 0x7f0d00c6
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Display3 = 0x7f0d0015
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Display2 = 0x7f0d0014
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Display1 = 0x7f0d0013
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Caption = 0x7f0d0012
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Button = 0x7f0d0011
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat = 0x7f0d000e
jp.co.cyberagent.android.gpuimage.sample:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f0d000d
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0d0021
jp.co.cyberagent.android.gpuimage.sample:style/Base.DialogWindowTitle.AppCompat = 0x7f0d000c
jp.co.cyberagent.android.gpuimage.sample:style/Base.Animation.AppCompat.Tooltip = 0x7f0d000b
jp.co.cyberagent.android.gpuimage.sample:style/Base.V21.Theme.AppCompat.Light = 0x7f0d0055
jp.co.cyberagent.android.gpuimage.sample:style/Base.Animation.AppCompat.DropDownUp = 0x7f0d000a
jp.co.cyberagent.android.gpuimage.sample:style/Base.Animation.AppCompat.Dialog = 0x7f0d0009
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f0d007e
jp.co.cyberagent.android.gpuimage.sample:style/AppTheme.NoActionBar = 0x7f0d0006
jp.co.cyberagent.android.gpuimage.sample:style/AppTheme = 0x7f0d0005
jp.co.cyberagent.android.gpuimage.sample:style/Animation.AppCompat.Tooltip = 0x7f0d0004
jp.co.cyberagent.android.gpuimage.sample:style/Animation.AppCompat.DropDownUp = 0x7f0d0003
jp.co.cyberagent.android.gpuimage.sample:style/Animation.AppCompat.Dialog = 0x7f0d0002
jp.co.cyberagent.android.gpuimage.sample:style/AlertDialog.AppCompat = 0x7f0d0000
jp.co.cyberagent.android.gpuimage.sample:style/ThemeOverlay.AppCompat = 0x7f0d010c
jp.co.cyberagent.android.gpuimage.sample:string/title_activity_activity_main = 0x7f0c001e
jp.co.cyberagent.android.gpuimage.sample:string/status_bar_notification_info_overflow = 0x7f0c001d
jp.co.cyberagent.android.gpuimage.sample:string/abc_toolbar_collapse_description = 0x7f0c001a
jp.co.cyberagent.android.gpuimage.sample:string/abc_shareactionprovider_share_with_application = 0x7f0c0019
jp.co.cyberagent.android.gpuimage.sample:style/Base.V23.Theme.AppCompat = 0x7f0d005a
jp.co.cyberagent.android.gpuimage.sample:string/abc_searchview_description_voice = 0x7f0c0017
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.NoActionBar = 0x7f0d010b
jp.co.cyberagent.android.gpuimage.sample:string/abc_searchview_description_submit = 0x7f0c0016
jp.co.cyberagent.android.gpuimage.sample:string/abc_searchview_description_search = 0x7f0c0015
jp.co.cyberagent.android.gpuimage.sample:string/abc_search_hint = 0x7f0c0012
jp.co.cyberagent.android.gpuimage.sample:styleable/View = 0x7f0e0028
jp.co.cyberagent.android.gpuimage.sample:string/abc_prepend_shortcut_label = 0x7f0c0011
jp.co.cyberagent.android.gpuimage.sample:string/abc_menu_sym_shortcut_label = 0x7f0c0010
jp.co.cyberagent.android.gpuimage.sample:string/abc_menu_meta_shortcut_label = 0x7f0c000d
jp.co.cyberagent.android.gpuimage.sample:style/Platform.AppCompat.Light = 0x7f0d00a4
jp.co.cyberagent.android.gpuimage.sample:string/abc_menu_delete_shortcut_label = 0x7f0c000a
jp.co.cyberagent.android.gpuimage.sample:string/abc_menu_ctrl_shortcut_label = 0x7f0c0009
jp.co.cyberagent.android.gpuimage.sample:string/abc_activity_chooser_view_see_all = 0x7f0c0004
jp.co.cyberagent.android.gpuimage.sample:string/abc_action_mode_done = 0x7f0c0003
jp.co.cyberagent.android.gpuimage.sample:string/abc_action_bar_up_description = 0x7f0c0001
jp.co.cyberagent.android.gpuimage.sample:string/abc_action_bar_home_description = 0x7f0c0000
jp.co.cyberagent.android.gpuimage.sample:raw/tone_cuver_sample = 0x7f0b0000
jp.co.cyberagent.android.gpuimage.sample:layout/select_dialog_multichoice_material = 0x7f0a0027
jp.co.cyberagent.android.gpuimage.sample:layout/notification_template_part_time = 0x7f0a0025
jp.co.cyberagent.android.gpuimage.sample:layout/notification_template_part_chronometer = 0x7f0a0024
jp.co.cyberagent.android.gpuimage.sample:layout/notification_template_icon_group = 0x7f0a0023
jp.co.cyberagent.android.gpuimage.sample:layout/notification_template_custom_big = 0x7f0a0022
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0d001b
jp.co.cyberagent.android.gpuimage.sample:layout/notification_action = 0x7f0a0020
jp.co.cyberagent.android.gpuimage.sample:layout/custom_dialog = 0x7f0a001f
jp.co.cyberagent.android.gpuimage.sample:layout/activity_gallery = 0x7f0a001d
jp.co.cyberagent.android.gpuimage.sample:layout/activity_camera = 0x7f0a001c
jp.co.cyberagent.android.gpuimage.sample:layout/abc_select_dialog_material = 0x7f0a001a
jp.co.cyberagent.android.gpuimage.sample:layout/abc_search_view = 0x7f0a0019
jp.co.cyberagent.android.gpuimage.sample:layout/abc_search_dropdown_item_icons_2line = 0x7f0a0018
jp.co.cyberagent.android.gpuimage.sample:layout/abc_screen_toolbar = 0x7f0a0017
jp.co.cyberagent.android.gpuimage.sample:layout/abc_screen_simple_overlay_action_mode = 0x7f0a0016
jp.co.cyberagent.android.gpuimage.sample:layout/abc_list_menu_item_radio = 0x7f0a0011
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f0d012c
jp.co.cyberagent.android.gpuimage.sample:layout/abc_list_menu_item_checkbox = 0x7f0a000e
jp.co.cyberagent.android.gpuimage.sample:string/abc_capital_on = 0x7f0c0007
jp.co.cyberagent.android.gpuimage.sample:layout/abc_alert_dialog_title_material = 0x7f0a000a
jp.co.cyberagent.android.gpuimage.sample:layout/abc_alert_dialog_material = 0x7f0a0009
jp.co.cyberagent.android.gpuimage.sample:layout/abc_alert_dialog_button_bar_material = 0x7f0a0008
jp.co.cyberagent.android.gpuimage.sample:layout/abc_activity_chooser_view = 0x7f0a0006
jp.co.cyberagent.android.gpuimage.sample:layout/abc_action_mode_bar = 0x7f0a0004
jp.co.cyberagent.android.gpuimage.sample:layout/abc_action_menu_item_layout = 0x7f0a0002
jp.co.cyberagent.android.gpuimage.sample:layout/abc_action_bar_up_container = 0x7f0a0001
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f0d013f
jp.co.cyberagent.android.gpuimage.sample:style/Base.Theme.AppCompat.Light = 0x7f0d0045
jp.co.cyberagent.android.gpuimage.sample:layout/abc_action_bar_title_item = 0x7f0a0000
jp.co.cyberagent.android.gpuimage.sample:interpolator/fast_out_slow_in = 0x7f090006
jp.co.cyberagent.android.gpuimage.sample:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f090004
jp.co.cyberagent.android.gpuimage.sample:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f090002
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Display4 = 0x7f0d0016
jp.co.cyberagent.android.gpuimage.sample:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f090000
jp.co.cyberagent.android.gpuimage.sample:integer/config_tooltipAnimTime = 0x7f080003
jp.co.cyberagent.android.gpuimage.sample:integer/cancel_button_image_alpha = 0x7f080002
jp.co.cyberagent.android.gpuimage.sample:integer/abc_config_activityShortDur = 0x7f080001
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Body2 = 0x7f0d00c0
jp.co.cyberagent.android.gpuimage.sample:id/wrap_content = 0x7f0700b0
jp.co.cyberagent.android.gpuimage.sample:id/withText = 0x7f0700af
jp.co.cyberagent.android.gpuimage.sample:id/up = 0x7f0700ad
jp.co.cyberagent.android.gpuimage.sample:attr/color = 0x7f02004b
jp.co.cyberagent.android.gpuimage.sample:bool/abc_allow_stacked_button_bar = 0x7f030001
jp.co.cyberagent.android.gpuimage.sample:id/uniform = 0x7f0700ac
jp.co.cyberagent.android.gpuimage.sample:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f0d0044
jp.co.cyberagent.android.gpuimage.sample:id/title = 0x7f0700a6
jp.co.cyberagent.android.gpuimage.sample:string/abc_action_menu_overflow_description = 0x7f0c0002
jp.co.cyberagent.android.gpuimage.sample:color/switch_thumb_material_dark = 0x7f040051
jp.co.cyberagent.android.gpuimage.sample:id/texture_view = 0x7f0700a4
jp.co.cyberagent.android.gpuimage.sample:id/textSpacerNoButtons = 0x7f0700a2
jp.co.cyberagent.android.gpuimage.sample:id/text2 = 0x7f0700a1
jp.co.cyberagent.android.gpuimage.sample:id/META = 0x7f070003
jp.co.cyberagent.android.gpuimage.sample:id/tag_accessibility_pane_title = 0x7f07009b
jp.co.cyberagent.android.gpuimage.sample:id/tabMode = 0x7f070097
jp.co.cyberagent.android.gpuimage.sample:attr/seekBarStyle = 0x7f0200ca
jp.co.cyberagent.android.gpuimage.sample:id/surface_view = 0x7f070096
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.PopupMenu = 0x7f0d0141
jp.co.cyberagent.android.gpuimage.sample:id/src_in = 0x7f070091
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.PopupMenu = 0x7f0d0091
jp.co.cyberagent.android.gpuimage.sample:id/split_action_bar = 0x7f07008f
jp.co.cyberagent.android.gpuimage.sample:styleable/AppCompatTheme = 0x7f0e000e
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Button.Borderless = 0x7f0d0121
jp.co.cyberagent.android.gpuimage.sample:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f0d00b9
jp.co.cyberagent.android.gpuimage.sample:id/showCustom = 0x7f07008b
jp.co.cyberagent.android.gpuimage.sample:id/seekBar = 0x7f070088
jp.co.cyberagent.android.gpuimage.sample:id/search_src_text = 0x7f070086
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.CompoundButton.Switch = 0x7f0d012a
jp.co.cyberagent.android.gpuimage.sample:id/search_plate = 0x7f070085
jp.co.cyberagent.android.gpuimage.sample:dimen/highlight_alpha_material_colored = 0x7f050057
jp.co.cyberagent.android.gpuimage.sample:id/search_edit_frame = 0x7f070082
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.Light.DarkActionBar = 0x7f0d0105
jp.co.cyberagent.android.gpuimage.sample:id/search_close_btn = 0x7f070081
jp.co.cyberagent.android.gpuimage.sample:id/search_button = 0x7f070080
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f0d0031
jp.co.cyberagent.android.gpuimage.sample:id/right_side = 0x7f070079
jp.co.cyberagent.android.gpuimage.sample:id/never = 0x7f07006c
jp.co.cyberagent.android.gpuimage.sample:attr/customNavigationLayout = 0x7f02005f
jp.co.cyberagent.android.gpuimage.sample:id/middle = 0x7f07006a
jp.co.cyberagent.android.gpuimage.sample:attr/paddingBottomNoButtons = 0x7f0200b4
jp.co.cyberagent.android.gpuimage.sample:id/search_go_btn = 0x7f070083
jp.co.cyberagent.android.gpuimage.sample:color/abc_tint_switch_track = 0x7f040018
jp.co.cyberagent.android.gpuimage.sample:id/listMode = 0x7f070067
jp.co.cyberagent.android.gpuimage.sample:style/Widget.Compat.NotificationActionText = 0x7f0d015f
jp.co.cyberagent.android.gpuimage.sample:id/italic = 0x7f070064
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ListView.Menu = 0x7f0d0149
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f0d0080
jp.co.cyberagent.android.gpuimage.sample:id/homeAsUp = 0x7f07005d
jp.co.cyberagent.android.gpuimage.sample:attr/colorControlActivated = 0x7f02004f
jp.co.cyberagent.android.gpuimage.sample:dimen/notification_right_icon_size = 0x7f050066
jp.co.cyberagent.android.gpuimage.sample:id/group_divider = 0x7f07005b
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_control_padding_material = 0x7f05001a
jp.co.cyberagent.android.gpuimage.sample:id/expanded_menu = 0x7f070058
jp.co.cyberagent.android.gpuimage.sample:id/end = 0x7f070056
jp.co.cyberagent.android.gpuimage.sample:id/edit_query = 0x7f070055
jp.co.cyberagent.android.gpuimage.sample:id/content = 0x7f07004d
jp.co.cyberagent.android.gpuimage.sample:id/disableHome = 0x7f070054
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ProgressBar = 0x7f0d0094
jp.co.cyberagent.android.gpuimage.sample:id/dialog_button = 0x7f070053
jp.co.cyberagent.android.gpuimage.sample:id/decor_content_parent = 0x7f070051
jp.co.cyberagent.android.gpuimage.sample:id/showHome = 0x7f07008c
jp.co.cyberagent.android.gpuimage.sample:id/center_vertical = 0x7f070048
jp.co.cyberagent.android.gpuimage.sample:id/tag_accessibility_heading = 0x7f07009a
jp.co.cyberagent.android.gpuimage.sample:color/secondary_text_default_material_light = 0x7f04004c
jp.co.cyberagent.android.gpuimage.sample:id/button_save = 0x7f070047
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_15 = 0x7f07000e
jp.co.cyberagent.android.gpuimage.sample:id/button_gallery = 0x7f070046
jp.co.cyberagent.android.gpuimage.sample:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f090001
jp.co.cyberagent.android.gpuimage.sample:id/button_capture = 0x7f070044
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_list_item_height_large_material = 0x7f050030
jp.co.cyberagent.android.gpuimage.sample:id/button_camera = 0x7f070043
jp.co.cyberagent.android.gpuimage.sample:id/buttonPanel = 0x7f070042
jp.co.cyberagent.android.gpuimage.sample:id/bottom = 0x7f070041
jp.co.cyberagent.android.gpuimage.sample:attr/actionBarStyle = 0x7f020005
jp.co.cyberagent.android.gpuimage.sample:id/blocking = 0x7f070040
jp.co.cyberagent.android.gpuimage.sample:id/beginning = 0x7f07003f
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f0d009c
jp.co.cyberagent.android.gpuimage.sample:id/async = 0x7f07003d
jp.co.cyberagent.android.gpuimage.sample:id/add = 0x7f07003a
jp.co.cyberagent.android.gpuimage.sample:attr/actionModePopupWindowStyle = 0x7f020017
jp.co.cyberagent.android.gpuimage.sample:id/action_mode_close_button = 0x7f070036
jp.co.cyberagent.android.gpuimage.sample:attr/actionViewClass = 0x7f020020
jp.co.cyberagent.android.gpuimage.sample:id/action_mode_bar = 0x7f070034
jp.co.cyberagent.android.gpuimage.sample:styleable/AppCompatTextHelper = 0x7f0e000c
jp.co.cyberagent.android.gpuimage.sample:id/action_menu_presenter = 0x7f070033
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_disabled_alpha_material_light = 0x7f050028
jp.co.cyberagent.android.gpuimage.sample:id/action_image = 0x7f070031
jp.co.cyberagent.android.gpuimage.sample:id/action_divider = 0x7f070030
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f060016
jp.co.cyberagent.android.gpuimage.sample:id/action_container = 0x7f07002e
jp.co.cyberagent.android.gpuimage.sample:id/action_bar_title = 0x7f07002d
jp.co.cyberagent.android.gpuimage.sample:string/abc_searchview_description_query = 0x7f0c0014
jp.co.cyberagent.android.gpuimage.sample:color/material_grey_900 = 0x7f04003e
jp.co.cyberagent.android.gpuimage.sample:id/action_bar_root = 0x7f07002a
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0d015d
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f0d008f
jp.co.cyberagent.android.gpuimage.sample:attr/ttcIndex = 0x7f02010b
jp.co.cyberagent.android.gpuimage.sample:id/action_bar = 0x7f070027
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0d00f3
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_9 = 0x7f070026
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.SearchView.ActionBar = 0x7f0d0153
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_7 = 0x7f070024
jp.co.cyberagent.android.gpuimage.sample:style/Base.V7.Theme.AppCompat = 0x7f0d0061
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_31 = 0x7f070020
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_29 = 0x7f07001d
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_28 = 0x7f07001c
jp.co.cyberagent.android.gpuimage.sample:id/action_bar_spinner = 0x7f07002b
jp.co.cyberagent.android.gpuimage.sample:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f0d0043
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_27 = 0x7f07001b
jp.co.cyberagent.android.gpuimage.sample:attr/colorControlNormal = 0x7f020051
jp.co.cyberagent.android.gpuimage.sample:attr/checkboxStyle = 0x7f020045
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f060052
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_25 = 0x7f070019
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Title.Inverse = 0x7f0d00d9
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_24 = 0x7f070018
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_22 = 0x7f070016
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_21 = 0x7f070015
jp.co.cyberagent.android.gpuimage.sample:styleable/ListPopupWindow = 0x7f0e001a
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_20 = 0x7f070014
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_19 = 0x7f070012
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_18 = 0x7f070011
jp.co.cyberagent.android.gpuimage.sample:color/highlighted_text_material_light = 0x7f040032
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_26 = 0x7f07001a
jp.co.cyberagent.android.gpuimage.sample:styleable/ButtonBarLayout = 0x7f0e000f
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_action_bar_stacked_max_height = 0x7f050009
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_star_black_48dp = 0x7f060023
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_17 = 0x7f070010
jp.co.cyberagent.android.gpuimage.sample:id/tag_screen_reader_focusable = 0x7f07009c
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_11 = 0x7f07000a
jp.co.cyberagent.android.gpuimage.sample:color/material_blue_grey_800 = 0x7f040033
jp.co.cyberagent.android.gpuimage.sample:color/bright_foreground_disabled_material_light = 0x7f040022
jp.co.cyberagent.android.gpuimage.sample:id/none = 0x7f07006d
jp.co.cyberagent.android.gpuimage.sample:attr/trackTintMode = 0x7f02010a
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_10 = 0x7f070009
jp.co.cyberagent.android.gpuimage.sample:id/SHIFT = 0x7f070004
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_0 = 0x7f070007
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_action_clickable_span = 0x7f070006
jp.co.cyberagent.android.gpuimage.sample:id/SYM = 0x7f070005
jp.co.cyberagent.android.gpuimage.sample:id/ALT = 0x7f070000
jp.co.cyberagent.android.gpuimage.sample:drawable/notification_bg_normal = 0x7f060067
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Button = 0x7f0d0120
jp.co.cyberagent.android.gpuimage.sample:drawable/notification_bg = 0x7f060063
jp.co.cyberagent.android.gpuimage.sample:drawable/notification_action_background = 0x7f060062
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Large = 0x7f0d0019
jp.co.cyberagent.android.gpuimage.sample:drawable/lookup_amatorka = 0x7f060061
jp.co.cyberagent.android.gpuimage.sample:drawable/ic_action_search = 0x7f06005e
jp.co.cyberagent.android.gpuimage.sample:styleable/FontFamilyFont = 0x7f0e0014
jp.co.cyberagent.android.gpuimage.sample:attr/windowMinWidthMinor = 0x7f020116
jp.co.cyberagent.android.gpuimage.sample:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f06005b
jp.co.cyberagent.android.gpuimage.sample:dimen/notification_large_icon_width = 0x7f050063
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_6 = 0x7f070023
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.Compat.Notification.Line2 = 0x7f0d00f0
jp.co.cyberagent.android.gpuimage.sample:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f060057
jp.co.cyberagent.android.gpuimage.sample:drawable/btn_checkbox_checked_mtrl = 0x7f060056
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_vector_test = 0x7f060055
jp.co.cyberagent.android.gpuimage.sample:id/search_voice_btn = 0x7f070087
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_textfield_default_mtrl_alpha = 0x7f060051
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_textfield_activated_mtrl_alpha = 0x7f060050
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_text_select_handle_right_mtrl_light = 0x7f06004f
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_3 = 0x7f07001e
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0d00eb
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_text_select_handle_right_mtrl_dark = 0x7f06004e
jp.co.cyberagent.android.gpuimage.sample:dimen/notification_big_circle_margin = 0x7f050060
jp.co.cyberagent.android.gpuimage.sample:id/topPanel = 0x7f0700aa
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_text_select_handle_middle_mtrl_light = 0x7f06004d
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.SeekBar.Discrete = 0x7f0d0155
jp.co.cyberagent.android.gpuimage.sample:attr/autoCompleteTextViewStyle = 0x7f02002b
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_text_select_handle_left_mtrl_light = 0x7f06004b
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_title_material_toolbar = 0x7f05004d
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_text_select_handle_left_mtrl_dark = 0x7f06004a
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_text_cursor_material = 0x7f060049
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_seekbar_track_material = 0x7f060042
jp.co.cyberagent.android.gpuimage.sample:layout/support_simple_spinner_dropdown_item = 0x7f0a0029
jp.co.cyberagent.android.gpuimage.sample:attr/tickMarkTintMode = 0x7f0200f6
jp.co.cyberagent.android.gpuimage.sample:id/customPanel = 0x7f070050
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_scrubber_track_mtrl_alpha = 0x7f06003f
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f06003d
jp.co.cyberagent.android.gpuimage.sample:attr/actionModeBackground = 0x7f020010
jp.co.cyberagent.android.gpuimage.sample:id/collapseActionView = 0x7f07004c
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f06003c
jp.co.cyberagent.android.gpuimage.sample:id/home = 0x7f07005c
jp.co.cyberagent.android.gpuimage.sample:styleable/AnimatedStateListDrawableTransition = 0x7f0e0009
jp.co.cyberagent.android.gpuimage.sample:id/action_mode_bar_stub = 0x7f070035
jp.co.cyberagent.android.gpuimage.sample:id/gpuimage = 0x7f07005a
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ratingbar_small_material = 0x7f06003a
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ratingbar_material = 0x7f060039
jp.co.cyberagent.android.gpuimage.sample:id/src_atop = 0x7f070090
jp.co.cyberagent.android.gpuimage.sample:string/abc_menu_function_shortcut_label = 0x7f0c000c
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_popup_background_mtrl_mult = 0x7f060037
jp.co.cyberagent.android.gpuimage.sample:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f0d004e
jp.co.cyberagent.android.gpuimage.sample:id/textSpacerNoTitle = 0x7f0700a3
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f0d0128
jp.co.cyberagent.android.gpuimage.sample:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f06005d
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_list_selector_holo_light = 0x7f060035
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.Compat.Notification = 0x7f0d00ee
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0d0030
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_list_selector_disabled_holo_light = 0x7f060033
jp.co.cyberagent.android.gpuimage.sample:attr/actionModeCopyDrawable = 0x7f020013
jp.co.cyberagent.android.gpuimage.sample:id/line3 = 0x7f070066
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_list_selector_disabled_holo_dark = 0x7f060032
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f0d0129
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_list_selector_background_transition_holo_dark = 0x7f060030
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_list_pressed_holo_light = 0x7f06002f
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_list_longpressed_holo = 0x7f06002d
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_list_focused_holo = 0x7f06002c
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f0d013d
jp.co.cyberagent.android.gpuimage.sample:attr/iconifiedByDefault = 0x7f020091
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_list_divider_material = 0x7f06002a
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_14 = 0x7f07000d
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_star_half_black_48dp = 0x7f060026
jp.co.cyberagent.android.gpuimage.sample:attr/subMenuArrow = 0x7f0200d8
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_star_half_black_36dp = 0x7f060025
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_1 = 0x7f070008
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_star_half_black_16dp = 0x7f060024
jp.co.cyberagent.android.gpuimage.sample:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f0d0049
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f06001f
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_spinner_mtrl_am_alpha = 0x7f060043
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f06001e
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f0d0130
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_menu_overflow_material = 0x7f06001c
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_2 = 0x7f070013
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f06001b
jp.co.cyberagent.android.gpuimage.sample:layout/activity_main = 0x7f0a001e
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f060018
jp.co.cyberagent.android.gpuimage.sample:styleable/GradientColorItem = 0x7f0e0017
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_edit_text_material = 0x7f060014
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ActionBar.TabView = 0x7f0d0119
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_control_background_material = 0x7f060012
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_cab_background_top_mtrl_alpha = 0x7f060011
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_cab_background_top_material = 0x7f060010
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.Compat.Notification.Title = 0x7f0d00f2
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.RatingBar = 0x7f0d0096
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f06000d
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f06000b
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_btn_radio_material_anim = 0x7f06000a
jp.co.cyberagent.android.gpuimage.sample:id/info = 0x7f070063
jp.co.cyberagent.android.gpuimage.sample:attr/editTextBackground = 0x7f020075
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_btn_radio_material = 0x7f060009
jp.co.cyberagent.android.gpuimage.sample:id/scrollIndicatorUp = 0x7f07007c
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f06000e
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_btn_default_mtrl_shape = 0x7f060008
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_btn_colored_material = 0x7f060007
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f060006
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_btn_check_material_anim = 0x7f060004
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_btn_check_material = 0x7f060003
jp.co.cyberagent.android.gpuimage.sample:layout/select_dialog_singlechoice_material = 0x7f0a0028
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_btn_borderless_material = 0x7f060002
jp.co.cyberagent.android.gpuimage.sample:string/search_menu_title = 0x7f0c001c
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_dialog_material_background = 0x7f060013
jp.co.cyberagent.android.gpuimage.sample:styleable/DrawerArrowToggle = 0x7f0e0012
jp.co.cyberagent.android.gpuimage.sample:id/message = 0x7f070069
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_action_bar_item_background_material = 0x7f060001
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0d00e7
jp.co.cyberagent.android.gpuimage.sample:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f060000
jp.co.cyberagent.android.gpuimage.sample:dimen/tooltip_vertical_padding = 0x7f050072
jp.co.cyberagent.android.gpuimage.sample:attr/colorBackgroundFloating = 0x7f02004d
jp.co.cyberagent.android.gpuimage.sample:id/always = 0x7f07003c
jp.co.cyberagent.android.gpuimage.sample:attr/tooltipText = 0x7f020107
jp.co.cyberagent.android.gpuimage.sample:id/image = 0x7f070061
jp.co.cyberagent.android.gpuimage.sample:layout/abc_popup_menu_item_layout = 0x7f0a0013
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_list_selector_holo_dark = 0x7f060034
jp.co.cyberagent.android.gpuimage.sample:dimen/tooltip_precise_anchor_threshold = 0x7f050071
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ButtonBar = 0x7f0d007a
jp.co.cyberagent.android.gpuimage.sample:dimen/notification_subtext_size = 0x7f05006a
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Spinner = 0x7f0d009d
jp.co.cyberagent.android.gpuimage.sample:id/titleDividerNoCustom = 0x7f0700a7
jp.co.cyberagent.android.gpuimage.sample:attr/tooltipFrameBackground = 0x7f020106
jp.co.cyberagent.android.gpuimage.sample:dimen/notification_small_icon_size_as_large = 0x7f050069
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0d002e
jp.co.cyberagent.android.gpuimage.sample:dimen/notification_right_side_padding_top = 0x7f050067
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_item_background_holo_light = 0x7f060029
jp.co.cyberagent.android.gpuimage.sample:dimen/notification_media_narrow_margin = 0x7f050065
jp.co.cyberagent.android.gpuimage.sample:color/material_grey_850 = 0x7f04003d
jp.co.cyberagent.android.gpuimage.sample:id/scrollIndicatorDown = 0x7f07007b
jp.co.cyberagent.android.gpuimage.sample:dimen/notification_main_column_padding_top = 0x7f050064
jp.co.cyberagent.android.gpuimage.sample:color/material_blue_grey_950 = 0x7f040035
jp.co.cyberagent.android.gpuimage.sample:dimen/hint_alpha_material_dark = 0x7f05005a
jp.co.cyberagent.android.gpuimage.sample:dimen/notification_large_icon_height = 0x7f050062
jp.co.cyberagent.android.gpuimage.sample:dimen/notification_action_icon_size = 0x7f05005e
jp.co.cyberagent.android.gpuimage.sample:id/checked = 0x7f07004a
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_star_black_16dp = 0x7f060021
jp.co.cyberagent.android.gpuimage.sample:dimen/hint_pressed_alpha_material_light = 0x7f05005d
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ListView.Menu = 0x7f0d0090
jp.co.cyberagent.android.gpuimage.sample:dimen/hint_pressed_alpha_material_dark = 0x7f05005c
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ActionBar.TabBar = 0x7f0d0117
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Body2 = 0x7f0d0010
jp.co.cyberagent.android.gpuimage.sample:id/tag_accessibility_actions = 0x7f070098
jp.co.cyberagent.android.gpuimage.sample:dimen/disabled_alpha_material_dark = 0x7f050055
jp.co.cyberagent.android.gpuimage.sample:dimen/compat_control_corner_material = 0x7f050052
jp.co.cyberagent.android.gpuimage.sample:layout/abc_list_menu_item_icon = 0x7f0a000f
jp.co.cyberagent.android.gpuimage.sample:id/tag_unhandled_key_event_manager = 0x7f07009e
jp.co.cyberagent.android.gpuimage.sample:style/Base.V22.Theme.AppCompat = 0x7f0d0058
jp.co.cyberagent.android.gpuimage.sample:id/img_switch_camera = 0x7f070062
jp.co.cyberagent.android.gpuimage.sample:attr/textAppearancePopupMenuHeader = 0x7f0200e8
jp.co.cyberagent.android.gpuimage.sample:dimen/compat_button_padding_horizontal_material = 0x7f050050
jp.co.cyberagent.android.gpuimage.sample:styleable/ActionMode = 0x7f0e0004
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_text_select_handle_middle_mtrl_dark = 0x7f06004c
jp.co.cyberagent.android.gpuimage.sample:dimen/compat_button_inset_horizontal_material = 0x7f05004e
jp.co.cyberagent.android.gpuimage.sample:id/action_bar_activity_content = 0x7f070028
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_title_material = 0x7f05004c
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f0d00cc
jp.co.cyberagent.android.gpuimage.sample:attr/paddingEnd = 0x7f0200b5
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_subtitle_material_toolbar = 0x7f05004b
jp.co.cyberagent.android.gpuimage.sample:dimen/notification_small_icon_background_padding = 0x7f050068
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_subhead_material = 0x7f05004a
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f0d0137
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_menu_header_material = 0x7f050047
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_large_material = 0x7f050045
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_display_1_material = 0x7f050040
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_5 = 0x7f070022
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_caption_material = 0x7f05003f
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_button_material = 0x7f05003e
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ActionBar = 0x7f0d0069
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_body_2_material = 0x7f05003d
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_body_1_material = 0x7f05003c
jp.co.cyberagent.android.gpuimage.sample:color/abc_primary_text_material_dark = 0x7f04000b
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_select_dialog_padding_start_material = 0x7f05003a
jp.co.cyberagent.android.gpuimage.sample:id/actions = 0x7f070038
jp.co.cyberagent.android.gpuimage.sample:id/multiply = 0x7f07006b
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_seekbar_track_progress_height_material = 0x7f050039
jp.co.cyberagent.android.gpuimage.sample:dimen/hint_alpha_material_light = 0x7f05005b
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_seekbar_track_background_height_material = 0x7f050038
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_search_view_preferred_width = 0x7f050037
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_search_view_preferred_height = 0x7f050036
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_progress_bar_height_material = 0x7f050035
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0d002c
jp.co.cyberagent.android.gpuimage.sample:anim/abc_fade_in = 0x7f010000
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_panel_menu_list_width = 0x7f050034
jp.co.cyberagent.android.gpuimage.sample:attr/panelMenuListWidth = 0x7f0200ba
jp.co.cyberagent.android.gpuimage.sample:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f060059
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f0d00e1
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_floating_window_z = 0x7f05002f
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_edit_text_inset_horizontal_material = 0x7f05002d
jp.co.cyberagent.android.gpuimage.sample:id/showTitle = 0x7f07008d
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_edit_text_inset_bottom_material = 0x7f05002c
jp.co.cyberagent.android.gpuimage.sample:dimen/tooltip_y_offset_touch = 0x7f050074
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_dropdownitem_text_padding_right = 0x7f05002b
jp.co.cyberagent.android.gpuimage.sample:attr/backgroundStacked = 0x7f020033
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_dialog_padding_top_material = 0x7f050025
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f0d00cb
jp.co.cyberagent.android.gpuimage.sample:drawable/ic_switch_camera = 0x7f060060
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_dialog_min_width_major = 0x7f050022
jp.co.cyberagent.android.gpuimage.sample:id/action_text = 0x7f070037
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_dialog_list_padding_top_no_title = 0x7f050021
jp.co.cyberagent.android.gpuimage.sample:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f0d0048
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f050020
jp.co.cyberagent.android.gpuimage.sample:id/src_over = 0x7f070092
jp.co.cyberagent.android.gpuimage.sample:styleable/Spinner = 0x7f0e0022
jp.co.cyberagent.android.gpuimage.sample:attr/editTextColor = 0x7f020076
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_dialog_fixed_width_major = 0x7f05001e
jp.co.cyberagent.android.gpuimage.sample:layout/abc_screen_simple = 0x7f0a0015
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_dialog_fixed_height_minor = 0x7f05001d
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_dialog_corner_radius_material = 0x7f05001b
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_control_inset_material = 0x7f050019
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_control_corner_material = 0x7f050018
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_config_prefDialogWidth = 0x7f050017
jp.co.cyberagent.android.gpuimage.sample:dimen/tooltip_y_offset_non_touch = 0x7f050073
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_cascading_menus_min_smallest_width = 0x7f050016
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_button_padding_vertical_material = 0x7f050015
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_button_inset_vertical_material = 0x7f050013
jp.co.cyberagent.android.gpuimage.sample:styleable/GPUImageView = 0x7f0e0015
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_alert_dialog_button_dimen = 0x7f050011
jp.co.cyberagent.android.gpuimage.sample:string/abc_capital_off = 0x7f0c0006
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_alert_dialog_button_bar_height = 0x7f050010
jp.co.cyberagent.android.gpuimage.sample:dimen/highlight_alpha_material_dark = 0x7f050058
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_action_button_min_width_overflow_material = 0x7f05000f
jp.co.cyberagent.android.gpuimage.sample:color/abc_search_url_text_normal = 0x7f04000e
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_action_button_min_height_material = 0x7f05000d
jp.co.cyberagent.android.gpuimage.sample:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f090003
jp.co.cyberagent.android.gpuimage.sample:anim/abc_popup_enter = 0x7f010003
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f05000c
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f05000b
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_display_4_material = 0x7f050043
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_action_bar_overflow_padding_start_material = 0x7f050008
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f050006
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f0d0098
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_action_bar_elevation_material = 0x7f050005
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_action_bar_default_padding_end_material = 0x7f050003
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f0d008d
jp.co.cyberagent.android.gpuimage.sample:attr/switchTextAppearance = 0x7f0200e2
jp.co.cyberagent.android.gpuimage.sample:id/right_icon = 0x7f070078
jp.co.cyberagent.android.gpuimage.sample:color/primary_text_disabled_material_light = 0x7f040048
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_action_bar_content_inset_material = 0x7f050000
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Large = 0x7f0d00c9
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ratingbar_indicator_material = 0x7f060038
jp.co.cyberagent.android.gpuimage.sample:styleable/ActionMenuView = 0x7f0e0003
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_textfield_search_material = 0x7f060054
jp.co.cyberagent.android.gpuimage.sample:attr/drawableLeftCompat = 0x7f02006b
jp.co.cyberagent.android.gpuimage.sample:color/tooltip_background_light = 0x7f040056
jp.co.cyberagent.android.gpuimage.sample:style/Base.AlertDialog.AppCompat.Light = 0x7f0d0008
jp.co.cyberagent.android.gpuimage.sample:attr/collapseIcon = 0x7f02004a
jp.co.cyberagent.android.gpuimage.sample:attr/colorControlHighlight = 0x7f020050
jp.co.cyberagent.android.gpuimage.sample:drawable/notification_bg_low = 0x7f060064
jp.co.cyberagent.android.gpuimage.sample:color/switch_thumb_normal_material_dark = 0x7f040053
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_30 = 0x7f07001f
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Body1 = 0x7f0d00bf
jp.co.cyberagent.android.gpuimage.sample:attr/dividerPadding = 0x7f020067
jp.co.cyberagent.android.gpuimage.sample:color/switch_thumb_material_light = 0x7f040052
jp.co.cyberagent.android.gpuimage.sample:attr/fontVariationSettings = 0x7f020084
jp.co.cyberagent.android.gpuimage.sample:drawable/notification_tile_bg = 0x7f06006c
jp.co.cyberagent.android.gpuimage.sample:id/spacer = 0x7f07008e
jp.co.cyberagent.android.gpuimage.sample:color/secondary_text_disabled_material_dark = 0x7f04004d
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0d00dd
jp.co.cyberagent.android.gpuimage.sample:style/Platform.V25.AppCompat.Light = 0x7f0d00ab
jp.co.cyberagent.android.gpuimage.sample:drawable/btn_radio_on_mtrl = 0x7f06005c
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_disabled_alpha_material_dark = 0x7f050027
jp.co.cyberagent.android.gpuimage.sample:attr/actionBarTheme = 0x7f020009
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f0d014e
jp.co.cyberagent.android.gpuimage.sample:id/unchecked = 0x7f0700ab
jp.co.cyberagent.android.gpuimage.sample:color/abc_tint_default = 0x7f040014
jp.co.cyberagent.android.gpuimage.sample:color/secondary_text_default_material_dark = 0x7f04004b
jp.co.cyberagent.android.gpuimage.sample:id/icon_group = 0x7f07005f
jp.co.cyberagent.android.gpuimage.sample:id/FUNCTION = 0x7f070002
jp.co.cyberagent.android.gpuimage.sample:drawable/notification_icon_background = 0x7f060069
jp.co.cyberagent.android.gpuimage.sample:attr/subtitle = 0x7f0200da
jp.co.cyberagent.android.gpuimage.sample:attr/listPreferredItemHeightLarge = 0x7f0200a3
jp.co.cyberagent.android.gpuimage.sample:color/ripple_material_light = 0x7f04004a
jp.co.cyberagent.android.gpuimage.sample:attr/listMenuViewStyle = 0x7f0200a0
jp.co.cyberagent.android.gpuimage.sample:attr/buttonTintMode = 0x7f020044
jp.co.cyberagent.android.gpuimage.sample:string/app_name = 0x7f0c001b
jp.co.cyberagent.android.gpuimage.sample:color/abc_secondary_text_material_dark = 0x7f040011
jp.co.cyberagent.android.gpuimage.sample:attr/drawableTopCompat = 0x7f020071
jp.co.cyberagent.android.gpuimage.sample:color/primary_text_disabled_material_dark = 0x7f040047
jp.co.cyberagent.android.gpuimage.sample:layout/abc_popup_menu_header_item_layout = 0x7f0a0012
jp.co.cyberagent.android.gpuimage.sample:attr/alphabeticModifiers = 0x7f020028
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_display_3_material = 0x7f050042
jp.co.cyberagent.android.gpuimage.sample:color/button_material_dark = 0x7f040027
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0d001c
jp.co.cyberagent.android.gpuimage.sample:color/primary_material_dark = 0x7f040043
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f0d0134
jp.co.cyberagent.android.gpuimage.sample:id/button_choose_filter = 0x7f070045
jp.co.cyberagent.android.gpuimage.sample:attr/alpha = 0x7f020027
jp.co.cyberagent.android.gpuimage.sample:color/primary_dark_material_light = 0x7f040042
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_switch_thumb_material = 0x7f060045
jp.co.cyberagent.android.gpuimage.sample:style/Base.V7.Widget.AppCompat.EditText = 0x7f0d0067
jp.co.cyberagent.android.gpuimage.sample:color/notification_icon_bg_color = 0x7f040040
jp.co.cyberagent.android.gpuimage.sample:color/notification_action_color_filter = 0x7f04003f
jp.co.cyberagent.android.gpuimage.sample:id/search_badge = 0x7f07007e
jp.co.cyberagent.android.gpuimage.sample:color/material_grey_100 = 0x7f040038
jp.co.cyberagent.android.gpuimage.sample:dimen/notification_content_margin_start = 0x7f050061
jp.co.cyberagent.android.gpuimage.sample:color/material_deep_teal_500 = 0x7f040037
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.TextView = 0x7f0d009f
jp.co.cyberagent.android.gpuimage.sample:attr/gapBetweenBars = 0x7f020086
jp.co.cyberagent.android.gpuimage.sample:attr/actionOverflowButtonStyle = 0x7f02001d
jp.co.cyberagent.android.gpuimage.sample:color/material_blue_grey_900 = 0x7f040034
jp.co.cyberagent.android.gpuimage.sample:attr/singleChoiceItemLayout = 0x7f0200d1
jp.co.cyberagent.android.gpuimage.sample:attr/textLocale = 0x7f0200ee
jp.co.cyberagent.android.gpuimage.sample:color/background_material_light = 0x7f040020
jp.co.cyberagent.android.gpuimage.sample:color/foreground_material_light = 0x7f040030
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_action_bar_content_inset_with_nav = 0x7f050001
jp.co.cyberagent.android.gpuimage.sample:color/foreground_material_dark = 0x7f04002f
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f0d006f
jp.co.cyberagent.android.gpuimage.sample:color/dim_foreground_material_light = 0x7f04002c
jp.co.cyberagent.android.gpuimage.sample:attr/selectableItemBackgroundBorderless = 0x7f0200cc
jp.co.cyberagent.android.gpuimage.sample:attr/colorPrimary = 0x7f020053
jp.co.cyberagent.android.gpuimage.sample:id/notification_main_column = 0x7f070070
jp.co.cyberagent.android.gpuimage.sample:attr/actionOverflowMenuStyle = 0x7f02001e
jp.co.cyberagent.android.gpuimage.sample:drawable/notification_template_icon_low_bg = 0x7f06006b
jp.co.cyberagent.android.gpuimage.sample:color/dim_foreground_material_dark = 0x7f04002b
jp.co.cyberagent.android.gpuimage.sample:color/bright_foreground_material_light = 0x7f040026
jp.co.cyberagent.android.gpuimage.sample:string/abc_shareactionprovider_share_with = 0x7f0c0018
jp.co.cyberagent.android.gpuimage.sample:attr/textAppearanceLargePopupMenu = 0x7f0200e4
jp.co.cyberagent.android.gpuimage.sample:id/parentPanel = 0x7f070074
jp.co.cyberagent.android.gpuimage.sample:color/bright_foreground_material_dark = 0x7f040025
jp.co.cyberagent.android.gpuimage.sample:attr/drawableRightCompat = 0x7f02006c
jp.co.cyberagent.android.gpuimage.sample:color/bright_foreground_inverse_material_dark = 0x7f040023
jp.co.cyberagent.android.gpuimage.sample:layout/abc_action_mode_close_item_material = 0x7f0a0005
jp.co.cyberagent.android.gpuimage.sample:layout/abc_action_menu_layout = 0x7f0a0003
jp.co.cyberagent.android.gpuimage.sample:color/background_material_dark = 0x7f04001f
jp.co.cyberagent.android.gpuimage.sample:attr/checkedTextViewStyle = 0x7f020046
jp.co.cyberagent.android.gpuimage.sample:color/bright_foreground_disabled_material_dark = 0x7f040021
jp.co.cyberagent.android.gpuimage.sample:color/abc_background_cache_hint_selector_material_light = 0x7f040001
jp.co.cyberagent.android.gpuimage.sample:id/activity_chooser_view_content = 0x7f070039
jp.co.cyberagent.android.gpuimage.sample:id/tag_unhandled_key_listeners = 0x7f07009f
jp.co.cyberagent.android.gpuimage.sample:id/scrollView = 0x7f07007d
jp.co.cyberagent.android.gpuimage.sample:id/search_bar = 0x7f07007f
jp.co.cyberagent.android.gpuimage.sample:id/normal = 0x7f07006e
jp.co.cyberagent.android.gpuimage.sample:attr/imageButtonStyle = 0x7f020092
jp.co.cyberagent.android.gpuimage.sample:attr/preserveIconSpacing = 0x7f0200be
jp.co.cyberagent.android.gpuimage.sample:color/background_floating_material_dark = 0x7f04001d
jp.co.cyberagent.android.gpuimage.sample:id/on = 0x7f070073
jp.co.cyberagent.android.gpuimage.sample:color/androidx_core_ripple_material_light = 0x7f04001b
jp.co.cyberagent.android.gpuimage.sample:attr/searchIcon = 0x7f0200c8
jp.co.cyberagent.android.gpuimage.sample:color/accent_material_light = 0x7f04001a
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_list_selector_background_transition_holo_light = 0x7f060031
jp.co.cyberagent.android.gpuimage.sample:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f0d0068
jp.co.cyberagent.android.gpuimage.sample:color/accent_material_dark = 0x7f040019
jp.co.cyberagent.android.gpuimage.sample:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f0d00b2
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_switch_track_mtrl_alpha = 0x7f060046
jp.co.cyberagent.android.gpuimage.sample:color/abc_tint_spinner = 0x7f040017
jp.co.cyberagent.android.gpuimage.sample:color/switch_thumb_disabled_material_dark = 0x7f04004f
jp.co.cyberagent.android.gpuimage.sample:dimen/notification_action_text_size = 0x7f05005f
jp.co.cyberagent.android.gpuimage.sample:color/button_material_light = 0x7f040028
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_seekbar_thumb_material = 0x7f060040
jp.co.cyberagent.android.gpuimage.sample:attr/buttonCompat = 0x7f02003d
jp.co.cyberagent.android.gpuimage.sample:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
jp.co.cyberagent.android.gpuimage.sample:attr/navigationMode = 0x7f0200b1
jp.co.cyberagent.android.gpuimage.sample:attr/iconTint = 0x7f02008f
jp.co.cyberagent.android.gpuimage.sample:color/abc_tint_btn_checkable = 0x7f040013
jp.co.cyberagent.android.gpuimage.sample:color/abc_secondary_text_material_light = 0x7f040012
jp.co.cyberagent.android.gpuimage.sample:attr/actionBarPopupTheme = 0x7f020002
jp.co.cyberagent.android.gpuimage.sample:id/expand_activities_button = 0x7f070057
jp.co.cyberagent.android.gpuimage.sample:styleable/SearchView = 0x7f0e0021
jp.co.cyberagent.android.gpuimage.sample:attr/backgroundTintMode = 0x7f020035
jp.co.cyberagent.android.gpuimage.sample:dimen/compat_button_padding_vertical_material = 0x7f050051
jp.co.cyberagent.android.gpuimage.sample:color/material_deep_teal_200 = 0x7f040036
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_cab_background_internal_bg = 0x7f06000f
jp.co.cyberagent.android.gpuimage.sample:color/abc_search_url_text = 0x7f04000d
jp.co.cyberagent.android.gpuimage.sample:attr/hideOnContentScroll = 0x7f02008b
jp.co.cyberagent.android.gpuimage.sample:attr/buttonStyle = 0x7f020041
jp.co.cyberagent.android.gpuimage.sample:color/abc_search_url_text_selected = 0x7f040010
jp.co.cyberagent.android.gpuimage.sample:attr/listLayout = 0x7f02009f
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_action_bar_overflow_padding_end_material = 0x7f050007
jp.co.cyberagent.android.gpuimage.sample:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f0d007f
jp.co.cyberagent.android.gpuimage.sample:color/abc_primary_text_material_light = 0x7f04000c
jp.co.cyberagent.android.gpuimage.sample:attr/autoSizePresetSizes = 0x7f02002e
jp.co.cyberagent.android.gpuimage.sample:color/abc_hint_foreground_material_dark = 0x7f040007
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_item_background_holo_dark = 0x7f060028
jp.co.cyberagent.android.gpuimage.sample:layout/abc_cascading_menu_item_layout = 0x7f0a000b
jp.co.cyberagent.android.gpuimage.sample:dimen/highlight_alpha_material_light = 0x7f050059
jp.co.cyberagent.android.gpuimage.sample:attr/drawableTintMode = 0x7f020070
jp.co.cyberagent.android.gpuimage.sample:attr/titleMarginStart = 0x7f0200fd
jp.co.cyberagent.android.gpuimage.sample:color/abc_background_cache_hint_selector_material_dark = 0x7f040000
jp.co.cyberagent.android.gpuimage.sample:style/Platform.ThemeOverlay.AppCompat = 0x7f0d00a5
jp.co.cyberagent.android.gpuimage.sample:style/Base.Theme.AppCompat = 0x7f0d003e
jp.co.cyberagent.android.gpuimage.sample:attr/defaultQueryHint = 0x7f020060
jp.co.cyberagent.android.gpuimage.sample:attr/progressBarPadding = 0x7f0200bf
jp.co.cyberagent.android.gpuimage.sample:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
jp.co.cyberagent.android.gpuimage.sample:bool/abc_config_actionMenuItemAllCaps = 0x7f030002
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_dropdownitem_text_padding_left = 0x7f05002a
jp.co.cyberagent.android.gpuimage.sample:attr/background = 0x7f020031
jp.co.cyberagent.android.gpuimage.sample:bool/abc_action_bar_embed_tabs = 0x7f030000
jp.co.cyberagent.android.gpuimage.sample:layout/abc_list_menu_item_layout = 0x7f0a0010
jp.co.cyberagent.android.gpuimage.sample:attr/windowNoTitle = 0x7f020117
jp.co.cyberagent.android.gpuimage.sample:drawable/btn_checkbox_unchecked_mtrl = 0x7f060058
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f06001a
jp.co.cyberagent.android.gpuimage.sample:attr/windowFixedHeightMinor = 0x7f020112
jp.co.cyberagent.android.gpuimage.sample:attr/windowActionBar = 0x7f02010e
jp.co.cyberagent.android.gpuimage.sample:attr/searchHintIcon = 0x7f0200c7
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f06003b
jp.co.cyberagent.android.gpuimage.sample:attr/dividerHorizontal = 0x7f020066
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_action_bar_default_padding_start_material = 0x7f050004
jp.co.cyberagent.android.gpuimage.sample:color/error_color_material_dark = 0x7f04002d
jp.co.cyberagent.android.gpuimage.sample:attr/voiceIcon = 0x7f02010d
jp.co.cyberagent.android.gpuimage.sample:id/submenuarrow = 0x7f070093
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0d00f4
jp.co.cyberagent.android.gpuimage.sample:attr/showDividers = 0x7f0200ce
jp.co.cyberagent.android.gpuimage.sample:attr/listItemLayout = 0x7f02009e
jp.co.cyberagent.android.gpuimage.sample:attr/autoSizeMinTextSize = 0x7f02002d
jp.co.cyberagent.android.gpuimage.sample:attr/viewInflaterClass = 0x7f02010c
jp.co.cyberagent.android.gpuimage.sample:layout/notification_action_tombstone = 0x7f0a0021
jp.co.cyberagent.android.gpuimage.sample:attr/track = 0x7f020108
jp.co.cyberagent.android.gpuimage.sample:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f0d00b8
jp.co.cyberagent.android.gpuimage.sample:color/primary_text_default_material_light = 0x7f040046
jp.co.cyberagent.android.gpuimage.sample:attr/trackTint = 0x7f020109
jp.co.cyberagent.android.gpuimage.sample:attr/toolbarNavigationButtonStyle = 0x7f020103
jp.co.cyberagent.android.gpuimage.sample:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f0d00af
jp.co.cyberagent.android.gpuimage.sample:attr/titleMarginEnd = 0x7f0200fc
jp.co.cyberagent.android.gpuimage.sample:attr/alertDialogTheme = 0x7f020025
jp.co.cyberagent.android.gpuimage.sample:color/abc_color_highlight_material = 0x7f040004
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f0d008a
jp.co.cyberagent.android.gpuimage.sample:attr/titleTextStyle = 0x7f020102
jp.co.cyberagent.android.gpuimage.sample:id/forever = 0x7f070059
jp.co.cyberagent.android.gpuimage.sample:attr/titleTextColor = 0x7f020101
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ListView.DropDown = 0x7f0d0148
jp.co.cyberagent.android.gpuimage.sample:attr/titleMarginBottom = 0x7f0200fb
jp.co.cyberagent.android.gpuimage.sample:attr/titleTextAppearance = 0x7f020100
jp.co.cyberagent.android.gpuimage.sample:attr/actionLayout = 0x7f02000d
jp.co.cyberagent.android.gpuimage.sample:id/action_bar_container = 0x7f070029
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f0d0024
jp.co.cyberagent.android.gpuimage.sample:attr/titleMargins = 0x7f0200ff
jp.co.cyberagent.android.gpuimage.sample:attr/buttonBarNeutralButtonStyle = 0x7f02003a
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_display_2_material = 0x7f050041
jp.co.cyberagent.android.gpuimage.sample:attr/tickMarkTint = 0x7f0200f5
jp.co.cyberagent.android.gpuimage.sample:attr/popupTheme = 0x7f0200bc
jp.co.cyberagent.android.gpuimage.sample:attr/tickMark = 0x7f0200f4
jp.co.cyberagent.android.gpuimage.sample:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0d004a
jp.co.cyberagent.android.gpuimage.sample:style/AlertDialog.AppCompat.Light = 0x7f0d0001
jp.co.cyberagent.android.gpuimage.sample:attr/ratingBarStyleSmall = 0x7f0200c6
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0d00e6
jp.co.cyberagent.android.gpuimage.sample:attr/thumbTintMode = 0x7f0200f3
jp.co.cyberagent.android.gpuimage.sample:id/title_template = 0x7f0700a8
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f06001d
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0d0123
jp.co.cyberagent.android.gpuimage.sample:attr/thumbTint = 0x7f0200f2
jp.co.cyberagent.android.gpuimage.sample:styleable/ActionBarLayout = 0x7f0e0001
jp.co.cyberagent.android.gpuimage.sample:attr/colorButtonNormal = 0x7f02004e
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f0d0139
jp.co.cyberagent.android.gpuimage.sample:attr/thumbTextPadding = 0x7f0200f1
jp.co.cyberagent.android.gpuimage.sample:color/background_floating_material_light = 0x7f04001e
jp.co.cyberagent.android.gpuimage.sample:id/progress_horizontal = 0x7f070076
jp.co.cyberagent.android.gpuimage.sample:attr/theme = 0x7f0200ef
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0d003b
jp.co.cyberagent.android.gpuimage.sample:attr/overlapAnchor = 0x7f0200b3
jp.co.cyberagent.android.gpuimage.sample:attr/textAppearanceSearchResultSubtitle = 0x7f0200e9
jp.co.cyberagent.android.gpuimage.sample:drawable/notify_panel_notification_icon_bg = 0x7f06006d
jp.co.cyberagent.android.gpuimage.sample:attr/icon = 0x7f02008e
jp.co.cyberagent.android.gpuimage.sample:attr/textAppearanceListItemSmall = 0x7f0200e7
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f0d0136
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_23 = 0x7f070017
jp.co.cyberagent.android.gpuimage.sample:style/ThemeOverlay.AppCompat.DayNight = 0x7f0d0110
jp.co.cyberagent.android.gpuimage.sample:attr/textAppearanceListItemSecondary = 0x7f0200e6
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0d0077
jp.co.cyberagent.android.gpuimage.sample:attr/listPreferredItemPaddingRight = 0x7f0200a7
jp.co.cyberagent.android.gpuimage.sample:attr/textAppearanceListItem = 0x7f0200e5
jp.co.cyberagent.android.gpuimage.sample:attr/progressBarStyle = 0x7f0200c0
jp.co.cyberagent.android.gpuimage.sample:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
jp.co.cyberagent.android.gpuimage.sample:attr/textAllCaps = 0x7f0200e3
jp.co.cyberagent.android.gpuimage.sample:id/submit_area = 0x7f070094
jp.co.cyberagent.android.gpuimage.sample:attr/switchStyle = 0x7f0200e1
jp.co.cyberagent.android.gpuimage.sample:string/abc_searchview_description_clear = 0x7f0c0013
jp.co.cyberagent.android.gpuimage.sample:id/action_bar_subtitle = 0x7f07002c
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_dialog_min_width_minor = 0x7f050023
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_8 = 0x7f070025
jp.co.cyberagent.android.gpuimage.sample:color/material_grey_50 = 0x7f04003a
jp.co.cyberagent.android.gpuimage.sample:id/progress_circular = 0x7f070075
jp.co.cyberagent.android.gpuimage.sample:attr/switchPadding = 0x7f0200e0
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Button = 0x7f0d0074
jp.co.cyberagent.android.gpuimage.sample:attr/switchMinWidth = 0x7f0200df
jp.co.cyberagent.android.gpuimage.sample:color/primary_text_default_material_dark = 0x7f040045
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0d00f5
jp.co.cyberagent.android.gpuimage.sample:attr/subtitleTextAppearance = 0x7f0200db
jp.co.cyberagent.android.gpuimage.sample:drawable/tooltip_frame_light = 0x7f06006f
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f060036
jp.co.cyberagent.android.gpuimage.sample:attr/closeIcon = 0x7f020047
jp.co.cyberagent.android.gpuimage.sample:attr/font = 0x7f02007b
jp.co.cyberagent.android.gpuimage.sample:attr/spinnerStyle = 0x7f0200d4
jp.co.cyberagent.android.gpuimage.sample:attr/contentInsetStart = 0x7f02005c
jp.co.cyberagent.android.gpuimage.sample:attr/backgroundTint = 0x7f020034
jp.co.cyberagent.android.gpuimage.sample:attr/colorSwitchThumbNormal = 0x7f020055
jp.co.cyberagent.android.gpuimage.sample:attr/showTitle = 0x7f0200d0
jp.co.cyberagent.android.gpuimage.sample:id/radio = 0x7f070077
jp.co.cyberagent.android.gpuimage.sample:attr/showAsAction = 0x7f0200cd
jp.co.cyberagent.android.gpuimage.sample:attr/logo = 0x7f0200a9
jp.co.cyberagent.android.gpuimage.sample:attr/selectableItemBackground = 0x7f0200cb
jp.co.cyberagent.android.gpuimage.sample:id/tag_transition_group = 0x7f07009d
jp.co.cyberagent.android.gpuimage.sample:id/shortcut = 0x7f07008a
jp.co.cyberagent.android.gpuimage.sample:attr/actionBarDivider = 0x7f020000
jp.co.cyberagent.android.gpuimage.sample:dimen/compat_notification_large_icon_max_width = 0x7f050054
jp.co.cyberagent.android.gpuimage.sample:color/primary_dark_material_dark = 0x7f040041
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_action_button_min_width_material = 0x7f05000e
jp.co.cyberagent.android.gpuimage.sample:color/abc_search_url_text_pressed = 0x7f04000f
jp.co.cyberagent.android.gpuimage.sample:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f0d004d
jp.co.cyberagent.android.gpuimage.sample:style/Base.AlertDialog.AppCompat = 0x7f0d0007
jp.co.cyberagent.android.gpuimage.sample:attr/title = 0x7f0200f9
jp.co.cyberagent.android.gpuimage.sample:attr/queryHint = 0x7f0200c2
jp.co.cyberagent.android.gpuimage.sample:attr/actionMenuTextColor = 0x7f02000f
jp.co.cyberagent.android.gpuimage.sample:attr/listPreferredItemPaddingStart = 0x7f0200a8
jp.co.cyberagent.android.gpuimage.sample:attr/divider = 0x7f020065
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Headline = 0x7f0d00c7
jp.co.cyberagent.android.gpuimage.sample:color/material_grey_800 = 0x7f04003c
jp.co.cyberagent.android.gpuimage.sample:attr/goIcon = 0x7f020087
jp.co.cyberagent.android.gpuimage.sample:id/select_dialog_listview = 0x7f070089
jp.co.cyberagent.android.gpuimage.sample:attr/textColorSearchUrl = 0x7f0200ed
jp.co.cyberagent.android.gpuimage.sample:id/search_mag_icon = 0x7f070084
jp.co.cyberagent.android.gpuimage.sample:attr/paddingStart = 0x7f0200b6
jp.co.cyberagent.android.gpuimage.sample:attr/popupWindowStyle = 0x7f0200bd
jp.co.cyberagent.android.gpuimage.sample:color/abc_btn_colored_borderless_text_material = 0x7f040002
jp.co.cyberagent.android.gpuimage.sample:attr/suggestionRowLayout = 0x7f0200de
jp.co.cyberagent.android.gpuimage.sample:styleable/AnimatedStateListDrawableCompat = 0x7f0e0007
jp.co.cyberagent.android.gpuimage.sample:color/highlighted_text_material_dark = 0x7f040031
jp.co.cyberagent.android.gpuimage.sample:attr/collapseContentDescription = 0x7f020049
jp.co.cyberagent.android.gpuimage.sample:anim/abc_slide_in_top = 0x7f010007
jp.co.cyberagent.android.gpuimage.sample:attr/popupMenuStyle = 0x7f0200bb
jp.co.cyberagent.android.gpuimage.sample:id/CTRL = 0x7f070001
jp.co.cyberagent.android.gpuimage.sample:attr/arrowHeadLength = 0x7f020029
jp.co.cyberagent.android.gpuimage.sample:attr/spinnerDropDownItemStyle = 0x7f0200d3
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_tab_indicator_mtrl_alpha = 0x7f060048
jp.co.cyberagent.android.gpuimage.sample:attr/tintMode = 0x7f0200f8
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Caption = 0x7f0d00c2
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_list_pressed_holo_dark = 0x7f06002e
jp.co.cyberagent.android.gpuimage.sample:attr/submitBackground = 0x7f0200d9
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.DrawerArrowToggle = 0x7f0d012b
jp.co.cyberagent.android.gpuimage.sample:attr/panelMenuListTheme = 0x7f0200b9
jp.co.cyberagent.android.gpuimage.sample:attr/panelBackground = 0x7f0200b8
jp.co.cyberagent.android.gpuimage.sample:attr/state_above_anchor = 0x7f0200d7
jp.co.cyberagent.android.gpuimage.sample:anim/abc_slide_out_top = 0x7f010009
jp.co.cyberagent.android.gpuimage.sample:attr/navigationIcon = 0x7f0200b0
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0d002a
jp.co.cyberagent.android.gpuimage.sample:anim/abc_popup_exit = 0x7f010004
jp.co.cyberagent.android.gpuimage.sample:attr/windowFixedWidthMajor = 0x7f020113
jp.co.cyberagent.android.gpuimage.sample:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f0d0057
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_12 = 0x7f07000b
jp.co.cyberagent.android.gpuimage.sample:attr/multiChoiceItemLayout = 0x7f0200ae
jp.co.cyberagent.android.gpuimage.sample:id/top = 0x7f0700a9
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Menu = 0x7f0d00d1
jp.co.cyberagent.android.gpuimage.sample:attr/contentInsetEnd = 0x7f020058
jp.co.cyberagent.android.gpuimage.sample:attr/logoDescription = 0x7f0200aa
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ListMenuView = 0x7f0d0145
jp.co.cyberagent.android.gpuimage.sample:attr/numericModifiers = 0x7f0200b2
jp.co.cyberagent.android.gpuimage.sample:attr/listPreferredItemPaddingEnd = 0x7f0200a5
jp.co.cyberagent.android.gpuimage.sample:styleable/LinearLayoutCompat_Layout = 0x7f0e0019
jp.co.cyberagent.android.gpuimage.sample:attr/iconTintMode = 0x7f020090
jp.co.cyberagent.android.gpuimage.sample:attr/autoSizeTextType = 0x7f020030
jp.co.cyberagent.android.gpuimage.sample:attr/menu = 0x7f0200ad
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.Compat.Notification.Info = 0x7f0d00ef
jp.co.cyberagent.android.gpuimage.sample:attr/windowFixedWidthMinor = 0x7f020114
jp.co.cyberagent.android.gpuimage.sample:attr/listPreferredItemPaddingLeft = 0x7f0200a6
jp.co.cyberagent.android.gpuimage.sample:attr/buttonBarNegativeButtonStyle = 0x7f020039
jp.co.cyberagent.android.gpuimage.sample:color/ripple_material_dark = 0x7f040049
jp.co.cyberagent.android.gpuimage.sample:color/androidx_core_secondary_text_default_material_light = 0x7f04001c
jp.co.cyberagent.android.gpuimage.sample:attr/homeAsUpIndicator = 0x7f02008c
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_edit_text_inset_top_material = 0x7f05002e
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0d002f
jp.co.cyberagent.android.gpuimage.sample:attr/actionBarTabStyle = 0x7f020007
jp.co.cyberagent.android.gpuimage.sample:id/alertTitle = 0x7f07003b
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f0d0133
jp.co.cyberagent.android.gpuimage.sample:attr/listPopupWindowStyle = 0x7f0200a1
jp.co.cyberagent.android.gpuimage.sample:dimen/disabled_alpha_material_light = 0x7f050056
jp.co.cyberagent.android.gpuimage.sample:attr/listDividerAlertDialog = 0x7f02009d
jp.co.cyberagent.android.gpuimage.sample:attr/displayOptions = 0x7f020064
jp.co.cyberagent.android.gpuimage.sample:styleable/AppCompatTextView = 0x7f0e000d
jp.co.cyberagent.android.gpuimage.sample:drawable/btn_radio_off_mtrl = 0x7f06005a
jp.co.cyberagent.android.gpuimage.sample:attr/allowStacking = 0x7f020026
jp.co.cyberagent.android.gpuimage.sample:styleable/TextAppearance = 0x7f0e0026
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0d008b
jp.co.cyberagent.android.gpuimage.sample:attr/listChoiceIndicatorSingleAnimated = 0x7f02009c
jp.co.cyberagent.android.gpuimage.sample:attr/fontProviderAuthority = 0x7f02007d
jp.co.cyberagent.android.gpuimage.sample:attr/actionButtonStyle = 0x7f02000b
jp.co.cyberagent.android.gpuimage.sample:attr/lastBaselineToBottomHeight = 0x7f020097
jp.co.cyberagent.android.gpuimage.sample:color/switch_thumb_disabled_material_light = 0x7f040050
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f0d00a0
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ImageButton = 0x7f0d0083
jp.co.cyberagent.android.gpuimage.sample:attr/maxButtonHeight = 0x7f0200ab
jp.co.cyberagent.android.gpuimage.sample:layout/abc_dialog_title_material = 0x7f0a000c
jp.co.cyberagent.android.gpuimage.sample:attr/actionBarItemBackground = 0x7f020001
jp.co.cyberagent.android.gpuimage.sample:id/bar = 0x7f07003e
jp.co.cyberagent.android.gpuimage.sample:attr/alertDialogCenterButtons = 0x7f020023
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.RatingBar.Small = 0x7f0d0151
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f0d00f9
jp.co.cyberagent.android.gpuimage.sample:attr/itemPadding = 0x7f020096
jp.co.cyberagent.android.gpuimage.sample:style/ThemeOverlay.AppCompat.ActionBar = 0x7f0d010d
jp.co.cyberagent.android.gpuimage.sample:attr/textColorAlertDialogListItem = 0x7f0200ec
jp.co.cyberagent.android.gpuimage.sample:attr/isLightTheme = 0x7f020095
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f0d0076
jp.co.cyberagent.android.gpuimage.sample:layout/abc_screen_content_include = 0x7f0a0014
jp.co.cyberagent.android.gpuimage.sample:anim/abc_slide_in_bottom = 0x7f010006
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_dialog_fixed_width_minor = 0x7f05001f
jp.co.cyberagent.android.gpuimage.sample:attr/indeterminateProgressStyle = 0x7f020093
jp.co.cyberagent.android.gpuimage.sample:attr/ratingBarStyle = 0x7f0200c4
jp.co.cyberagent.android.gpuimage.sample:attr/searchViewStyle = 0x7f0200c9
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f0d001e
jp.co.cyberagent.android.gpuimage.sample:attr/subtitleTextStyle = 0x7f0200dd
jp.co.cyberagent.android.gpuimage.sample:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f090005
jp.co.cyberagent.android.gpuimage.sample:id/screen = 0x7f07007a
jp.co.cyberagent.android.gpuimage.sample:attr/actionModeStyle = 0x7f02001b
jp.co.cyberagent.android.gpuimage.sample:integer/status_bar_notification_info_maxnum = 0x7f080004
jp.co.cyberagent.android.gpuimage.sample:attr/gpuimage_surface_type = 0x7f020089
jp.co.cyberagent.android.gpuimage.sample:anim/abc_fade_out = 0x7f010001
jp.co.cyberagent.android.gpuimage.sample:attr/fontProviderPackage = 0x7f020081
jp.co.cyberagent.android.gpuimage.sample:attr/contentDescription = 0x7f020057
jp.co.cyberagent.android.gpuimage.sample:attr/controlBackground = 0x7f02005e
jp.co.cyberagent.android.gpuimage.sample:attr/fontProviderFetchStrategy = 0x7f02007f
jp.co.cyberagent.android.gpuimage.sample:attr/buttonPanelSideLayout = 0x7f020040
jp.co.cyberagent.android.gpuimage.sample:attr/fontProviderCerts = 0x7f02007e
jp.co.cyberagent.android.gpuimage.sample:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f0d00b0
jp.co.cyberagent.android.gpuimage.sample:style/Base.ThemeOverlay.AppCompat = 0x7f0d004c
jp.co.cyberagent.android.gpuimage.sample:color/error_color_material_light = 0x7f04002e
jp.co.cyberagent.android.gpuimage.sample:attr/listPreferredItemHeight = 0x7f0200a2
jp.co.cyberagent.android.gpuimage.sample:attr/firstBaselineToTopHeight = 0x7f02007a
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_4 = 0x7f070021
jp.co.cyberagent.android.gpuimage.sample:id/notification_main_column_container = 0x7f070071
jp.co.cyberagent.android.gpuimage.sample:attr/expandActivityOverflowButtonDrawable = 0x7f020079
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_dialog_fixed_height_major = 0x7f05001c
jp.co.cyberagent.android.gpuimage.sample:attr/autoSizeStepGranularity = 0x7f02002f
jp.co.cyberagent.android.gpuimage.sample:dimen/tooltip_margin = 0x7f05006f
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_button_padding_horizontal_material = 0x7f050014
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0d002b
jp.co.cyberagent.android.gpuimage.sample:id/tag_accessibility_clickable_spans = 0x7f070099
jp.co.cyberagent.android.gpuimage.sample:attr/queryBackground = 0x7f0200c1
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0d0086
jp.co.cyberagent.android.gpuimage.sample:attr/actionBarSize = 0x7f020003
jp.co.cyberagent.android.gpuimage.sample:attr/drawerArrowStyle = 0x7f020072
jp.co.cyberagent.android.gpuimage.sample:layout/abc_tooltip = 0x7f0a001b
jp.co.cyberagent.android.gpuimage.sample:attr/ratingBarStyleIndicator = 0x7f0200c5
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_dialog_title_divider_material = 0x7f050026
jp.co.cyberagent.android.gpuimage.sample:attr/actionModeWebSearchDrawable = 0x7f02001c
jp.co.cyberagent.android.gpuimage.sample:attr/navigationContentDescription = 0x7f0200af
jp.co.cyberagent.android.gpuimage.sample:drawable/tooltip_frame_dark = 0x7f06006e
jp.co.cyberagent.android.gpuimage.sample:attr/drawableEndCompat = 0x7f02006a
jp.co.cyberagent.android.gpuimage.sample:color/bright_foreground_inverse_material_light = 0x7f040024
jp.co.cyberagent.android.gpuimage.sample:attr/dividerVertical = 0x7f020068
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_star_black_36dp = 0x7f060022
jp.co.cyberagent.android.gpuimage.sample:attr/homeLayout = 0x7f02008d
jp.co.cyberagent.android.gpuimage.sample:id/custom = 0x7f07004f
jp.co.cyberagent.android.gpuimage.sample:id/action_context_bar = 0x7f07002f
jp.co.cyberagent.android.gpuimage.sample:attr/dialogCornerRadius = 0x7f020061
jp.co.cyberagent.android.gpuimage.sample:attr/actionModeSelectAllDrawable = 0x7f020018
jp.co.cyberagent.android.gpuimage.sample:attr/contentInsetStartWithNavigation = 0x7f02005d
jp.co.cyberagent.android.gpuimage.sample:attr/contentInsetEndWithActions = 0x7f020059
jp.co.cyberagent.android.gpuimage.sample:dimen/notification_top_pad = 0x7f05006b
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.ActionBar = 0x7f0d012f
jp.co.cyberagent.android.gpuimage.sample:attr/fontProviderFetchTimeout = 0x7f020080
jp.co.cyberagent.android.gpuimage.sample:attr/dialogPreferredPadding = 0x7f020062
jp.co.cyberagent.android.gpuimage.sample:attr/arrowShaftLength = 0x7f02002a
jp.co.cyberagent.android.gpuimage.sample:attr/actionModeCloseButtonStyle = 0x7f020011
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0d002d
jp.co.cyberagent.android.gpuimage.sample:attr/contentInsetRight = 0x7f02005b
jp.co.cyberagent.android.gpuimage.sample:id/notification_background = 0x7f07006f
jp.co.cyberagent.android.gpuimage.sample:integer/abc_config_activityDefaultDur = 0x7f080000
jp.co.cyberagent.android.gpuimage.sample:attr/backgroundSplit = 0x7f020032
jp.co.cyberagent.android.gpuimage.sample:attr/actionModeCloseDrawable = 0x7f020012
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_seekbar_tick_mark_material = 0x7f060041
jp.co.cyberagent.android.gpuimage.sample:attr/listChoiceIndicatorMultipleAnimated = 0x7f02009b
jp.co.cyberagent.android.gpuimage.sample:attr/contentInsetLeft = 0x7f02005a
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Small.Inverse = 0x7f0d00d5
jp.co.cyberagent.android.gpuimage.sample:dimen/compat_notification_large_icon_max_height = 0x7f050053
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_voice_search_api_material = 0x7f060027
jp.co.cyberagent.android.gpuimage.sample:attr/commitIcon = 0x7f020056
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_spinner_textfield_background_material = 0x7f060044
jp.co.cyberagent.android.gpuimage.sample:attr/colorPrimaryDark = 0x7f020054
jp.co.cyberagent.android.gpuimage.sample:color/dim_foreground_disabled_material_light = 0x7f04002a
jp.co.cyberagent.android.gpuimage.sample:attr/windowMinWidthMajor = 0x7f020115
jp.co.cyberagent.android.gpuimage.sample:color/switch_thumb_normal_material_light = 0x7f040054
jp.co.cyberagent.android.gpuimage.sample:string/abc_menu_enter_shortcut_label = 0x7f0c000b
jp.co.cyberagent.android.gpuimage.sample:attr/dialogTheme = 0x7f020063
jp.co.cyberagent.android.gpuimage.sample:attr/colorError = 0x7f020052
jp.co.cyberagent.android.gpuimage.sample:attr/fontStyle = 0x7f020083
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_list_item_padding_horizontal_material = 0x7f050033
jp.co.cyberagent.android.gpuimage.sample:drawable/notification_template_icon_bg = 0x7f06006a
jp.co.cyberagent.android.gpuimage.sample:attr/toolbarStyle = 0x7f020104
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_tab_indicator_material = 0x7f060047
jp.co.cyberagent.android.gpuimage.sample:attr/srcCompat = 0x7f0200d6
jp.co.cyberagent.android.gpuimage.sample:attr/colorAccent = 0x7f02004c
jp.co.cyberagent.android.gpuimage.sample:color/abc_primary_text_disable_only_material_light = 0x7f04000a
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.Dialog.MinWidth = 0x7f0d0101
jp.co.cyberagent.android.gpuimage.sample:attr/paddingTopNoTitle = 0x7f0200b7
jp.co.cyberagent.android.gpuimage.sample:attr/autoSizeMaxTextSize = 0x7f02002c
jp.co.cyberagent.android.gpuimage.sample:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
jp.co.cyberagent.android.gpuimage.sample:attr/layout = 0x7f020098
jp.co.cyberagent.android.gpuimage.sample:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f0d00bd
jp.co.cyberagent.android.gpuimage.sample:attr/buttonBarButtonStyle = 0x7f020038
jp.co.cyberagent.android.gpuimage.sample:anim/abc_slide_out_bottom = 0x7f010008
jp.co.cyberagent.android.gpuimage.sample:attr/actionDropDownStyle = 0x7f02000c
jp.co.cyberagent.android.gpuimage.sample:id/time = 0x7f0700a5
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_go_search_api_material = 0x7f060019
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f0d006c
jp.co.cyberagent.android.gpuimage.sample:dimen/tooltip_precise_anchor_extra_offset = 0x7f050070
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_switch_padding = 0x7f05003b
jp.co.cyberagent.android.gpuimage.sample:color/abc_hint_foreground_material_light = 0x7f040008
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Toolbar = 0x7f0d015c
jp.co.cyberagent.android.gpuimage.sample:id/contentPanel = 0x7f07004e
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Title = 0x7f0d0027
jp.co.cyberagent.android.gpuimage.sample:attr/showText = 0x7f0200cf
jp.co.cyberagent.android.gpuimage.sample:attr/buttonGravity = 0x7f02003e
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_action_bar_stacked_tab_max_width = 0x7f05000a
jp.co.cyberagent.android.gpuimage.sample:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f0d00d7
jp.co.cyberagent.android.gpuimage.sample:attr/alertDialogStyle = 0x7f020024
jp.co.cyberagent.android.gpuimage.sample:attr/splitTrack = 0x7f0200d5
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_small_material = 0x7f050049
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0d0088
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f0d0085
jp.co.cyberagent.android.gpuimage.sample:attr/buttonBarStyle = 0x7f02003c
jp.co.cyberagent.android.gpuimage.sample:attr/dropDownListViewStyle = 0x7f020073
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0d007b
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0d0038
jp.co.cyberagent.android.gpuimage.sample:attr/buttonBarPositiveButtonStyle = 0x7f02003b
jp.co.cyberagent.android.gpuimage.sample:string/abc_menu_shift_shortcut_label = 0x7f0c000e
jp.co.cyberagent.android.gpuimage.sample:attr/windowActionBarOverlay = 0x7f02010f
jp.co.cyberagent.android.gpuimage.sample:attr/measureWithLargestChild = 0x7f0200ac
jp.co.cyberagent.android.gpuimage.sample:color/abc_tint_seek_thumb = 0x7f040016
jp.co.cyberagent.android.gpuimage.sample:style/Platform.V25.AppCompat = 0x7f0d00aa
jp.co.cyberagent.android.gpuimage.sample:id/line1 = 0x7f070065
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f0d007d
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_menu_material = 0x7f050048
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_list_item_height_small_material = 0x7f050032
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f060053
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f0d0089
jp.co.cyberagent.android.gpuimage.sample:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f0d0050
jp.co.cyberagent.android.gpuimage.sample:dimen/tooltip_corner_radius = 0x7f05006d
jp.co.cyberagent.android.gpuimage.sample:styleable/GradientColor = 0x7f0e0016
jp.co.cyberagent.android.gpuimage.sample:color/abc_decor_view_status_guard_light = 0x7f040006
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ActionBar.Solid = 0x7f0d0116
jp.co.cyberagent.android.gpuimage.sample:id/off = 0x7f070072
jp.co.cyberagent.android.gpuimage.sample:styleable/SwitchCompat = 0x7f0e0025
jp.co.cyberagent.android.gpuimage.sample:styleable/StateListDrawable = 0x7f0e0023
jp.co.cyberagent.android.gpuimage.sample:drawable/notification_bg_low_normal = 0x7f060065
jp.co.cyberagent.android.gpuimage.sample:id/action_menu_divider = 0x7f070032
jp.co.cyberagent.android.gpuimage.sample:attr/barLength = 0x7f020036
jp.co.cyberagent.android.gpuimage.sample:attr/subtitleTextColor = 0x7f0200dc
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_dialog_padding_material = 0x7f050024
jp.co.cyberagent.android.gpuimage.sample:attr/drawableBottomCompat = 0x7f020069
jp.co.cyberagent.android.gpuimage.sample:attr/actionModeFindDrawable = 0x7f020015
jp.co.cyberagent.android.gpuimage.sample:attr/editTextStyle = 0x7f020077
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f0d00fe
jp.co.cyberagent.android.gpuimage.sample:id/chronometer = 0x7f07004b
jp.co.cyberagent.android.gpuimage.sample:style/Base.V26.Theme.AppCompat = 0x7f0d005c
jp.co.cyberagent.android.gpuimage.sample:attr/textAppearanceSearchResultTitle = 0x7f0200ea
jp.co.cyberagent.android.gpuimage.sample:attr/actionModePasteDrawable = 0x7f020016
jp.co.cyberagent.android.gpuimage.sample:color/material_grey_300 = 0x7f040039
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f0d0029
jp.co.cyberagent.android.gpuimage.sample:id/ifRoom = 0x7f070060
jp.co.cyberagent.android.gpuimage.sample:style/Base.V26.Theme.AppCompat.Light = 0x7f0d005d
jp.co.cyberagent.android.gpuimage.sample:attr/activityChooserViewStyle = 0x7f020021
jp.co.cyberagent.android.gpuimage.sample:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
jp.co.cyberagent.android.gpuimage.sample:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
jp.co.cyberagent.android.gpuimage.sample:styleable/FontFamily = 0x7f0e0013
jp.co.cyberagent.android.gpuimage.sample:attr/closeItemLayout = 0x7f020048
jp.co.cyberagent.android.gpuimage.sample:attr/textAppearanceSmallPopupMenu = 0x7f0200eb
jp.co.cyberagent.android.gpuimage.sample:styleable/ActionMenuItemView = 0x7f0e0002
jp.co.cyberagent.android.gpuimage.sample:attr/fontFamily = 0x7f02007c
jp.co.cyberagent.android.gpuimage.sample:drawable/notification_bg_low_pressed = 0x7f060066
jp.co.cyberagent.android.gpuimage.sample:attr/alertDialogButtonGroupStyle = 0x7f020022
jp.co.cyberagent.android.gpuimage.sample:attr/spinBars = 0x7f0200d2
jp.co.cyberagent.android.gpuimage.sample:attr/tooltipForegroundColor = 0x7f020105
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f0d0131
jp.co.cyberagent.android.gpuimage.sample:attr/dropdownListPreferredItemHeight = 0x7f020074
jp.co.cyberagent.android.gpuimage.sample:string/abc_activitychooserview_choose_application = 0x7f0c0005
jp.co.cyberagent.android.gpuimage.sample:attr/drawableStartCompat = 0x7f02006e
jp.co.cyberagent.android.gpuimage.sample:layout/select_dialog_item_material = 0x7f0a0026
jp.co.cyberagent.android.gpuimage.sample:attr/listChoiceBackgroundIndicator = 0x7f02009a
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_action_bar_default_height_material = 0x7f050002
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.PopupWindow = 0x7f0d014c
jp.co.cyberagent.android.gpuimage.sample:color/dim_foreground_disabled_material_dark = 0x7f040029
jp.co.cyberagent.android.gpuimage.sample:attr/actionBarTabBarStyle = 0x7f020006
jp.co.cyberagent.android.gpuimage.sample:attr/gpuimage_show_loading = 0x7f020088
jp.co.cyberagent.android.gpuimage.sample:anim/abc_tooltip_exit = 0x7f01000b
jp.co.cyberagent.android.gpuimage.sample:attr/titleMarginTop = 0x7f0200fe
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_headline_material = 0x7f050044
jp.co.cyberagent.android.gpuimage.sample:color/abc_tint_edittext = 0x7f040015
jp.co.cyberagent.android.gpuimage.sample:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
jp.co.cyberagent.android.gpuimage.sample:id/surfaceView = 0x7f070095
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f0d0026
jp.co.cyberagent.android.gpuimage.sample:attr/actionModeShareDrawable = 0x7f020019
jp.co.cyberagent.android.gpuimage.sample:color/material_grey_600 = 0x7f04003b
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f0d006b
jp.co.cyberagent.android.gpuimage.sample:attr/actionMenuTextAppearance = 0x7f02000e
jp.co.cyberagent.android.gpuimage.sample:attr/actionModeCutDrawable = 0x7f020014
jp.co.cyberagent.android.gpuimage.sample:styleable/ViewBackgroundHelper = 0x7f0e0029
jp.co.cyberagent.android.gpuimage.sample:color/abc_decor_view_status_guard = 0x7f040005
jp.co.cyberagent.android.gpuimage.sample:dimen/tooltip_horizontal_padding = 0x7f05006e
jp.co.cyberagent.android.gpuimage.sample:attr/drawableSize = 0x7f02006d
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ImageButton = 0x7f0d012e
jp.co.cyberagent.android.gpuimage.sample:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f0d0046
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_ab_back_material = 0x7f060015
jp.co.cyberagent.android.gpuimage.sample:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f0d0072
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_dropdownitem_icon_width = 0x7f050029
jp.co.cyberagent.android.gpuimage.sample:attr/actionBarWidgetTheme = 0x7f02000a
jp.co.cyberagent.android.gpuimage.sample:attr/thickness = 0x7f0200f0
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Small = 0x7f0d0023
jp.co.cyberagent.android.gpuimage.sample:attr/listPreferredItemHeightSmall = 0x7f0200a4
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f06003e
jp.co.cyberagent.android.gpuimage.sample:attr/actionBarTabTextStyle = 0x7f020008
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f06000c
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_text_size_medium_material = 0x7f050046
jp.co.cyberagent.android.gpuimage.sample:attr/actionModeSplitBackground = 0x7f02001a
jp.co.cyberagent.android.gpuimage.sample:attr/radioButtonStyle = 0x7f0200c3
jp.co.cyberagent.android.gpuimage.sample:color/secondary_text_disabled_material_light = 0x7f04004e
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_16 = 0x7f07000f
jp.co.cyberagent.android.gpuimage.sample:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f0d00ad
jp.co.cyberagent.android.gpuimage.sample:attr/actionBarSplitStyle = 0x7f020004
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.ProgressBar = 0x7f0d014d
jp.co.cyberagent.android.gpuimage.sample:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0d0113
jp.co.cyberagent.android.gpuimage.sample:attr/borderlessButtonStyle = 0x7f020037
jp.co.cyberagent.android.gpuimage.sample:attr/lineHeight = 0x7f020099
jp.co.cyberagent.android.gpuimage.sample:string/abc_menu_space_shortcut_label = 0x7f0c000f
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_clear_material = 0x7f060017
jp.co.cyberagent.android.gpuimage.sample:color/primary_material_light = 0x7f040044
jp.co.cyberagent.android.gpuimage.sample:color/abc_primary_text_disable_only_material_dark = 0x7f040009
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f060005
jp.co.cyberagent.android.gpuimage.sample:attr/windowActionModeOverlay = 0x7f020110
jp.co.cyberagent.android.gpuimage.sample:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Body1 = 0x7f0d000f
jp.co.cyberagent.android.gpuimage.sample:id/icon = 0x7f07005e
jp.co.cyberagent.android.gpuimage.sample:attr/drawableTint = 0x7f02006f
jp.co.cyberagent.android.gpuimage.sample:id/accessibility_custom_action_13 = 0x7f07000c
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_list_divider_mtrl_alpha = 0x7f06002b
jp.co.cyberagent.android.gpuimage.sample:style/Base.TextAppearance.AppCompat.Headline = 0x7f0d0017
jp.co.cyberagent.android.gpuimage.sample:attr/windowFixedHeightMajor = 0x7f020111
jp.co.cyberagent.android.gpuimage.sample:anim/abc_grow_fade_in_from_bottom = 0x7f010002
jp.co.cyberagent.android.gpuimage.sample:style/Platform.Widget.AppCompat.Spinner = 0x7f0d00ac
jp.co.cyberagent.android.gpuimage.sample:id/list_item = 0x7f070068
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_button_inset_horizontal_material = 0x7f050012
jp.co.cyberagent.android.gpuimage.sample:drawable/abc_ic_search_api_material = 0x7f060020
jp.co.cyberagent.android.gpuimage.sample:attr/initialActivityCount = 0x7f020094
jp.co.cyberagent.android.gpuimage.sample:styleable/AppCompatSeekBar = 0x7f0e000b
jp.co.cyberagent.android.gpuimage.sample:attr/titleMargin = 0x7f0200fa
jp.co.cyberagent.android.gpuimage.sample:string/abc_menu_alt_shortcut_label = 0x7f0c0008
jp.co.cyberagent.android.gpuimage.sample:anim/abc_tooltip_enter = 0x7f01000a
jp.co.cyberagent.android.gpuimage.sample:attr/fontProviderQuery = 0x7f020082
jp.co.cyberagent.android.gpuimage.sample:id/checkbox = 0x7f070049
jp.co.cyberagent.android.gpuimage.sample:attr/elevation = 0x7f020078
jp.co.cyberagent.android.gpuimage.sample:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
jp.co.cyberagent.android.gpuimage.sample:id/text = 0x7f0700a0
jp.co.cyberagent.android.gpuimage.sample:color/abc_btn_colored_text_material = 0x7f040003
jp.co.cyberagent.android.gpuimage.sample:style/Widget.AppCompat.Light.SearchView = 0x7f0d0143
jp.co.cyberagent.android.gpuimage.sample:id/default_activity_button = 0x7f070052
jp.co.cyberagent.android.gpuimage.sample:drawable/ic_launcher = 0x7f06005f
jp.co.cyberagent.android.gpuimage.sample:attr/buttonStyleSmall = 0x7f020042
jp.co.cyberagent.android.gpuimage.sample:layout/abc_activity_chooser_view_list_item = 0x7f0a0007
jp.co.cyberagent.android.gpuimage.sample:id/useLogo = 0x7f0700ae
jp.co.cyberagent.android.gpuimage.sample:dimen/notification_top_pad_large_text = 0x7f05006c
jp.co.cyberagent.android.gpuimage.sample:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
jp.co.cyberagent.android.gpuimage.sample:attr/tint = 0x7f0200f7
jp.co.cyberagent.android.gpuimage.sample:attr/buttonIconDimen = 0x7f02003f
jp.co.cyberagent.android.gpuimage.sample:attr/actionProviderClass = 0x7f02001f
jp.co.cyberagent.android.gpuimage.sample:layout/abc_expanded_menu_layout = 0x7f0a000d
jp.co.cyberagent.android.gpuimage.sample:attr/buttonTint = 0x7f020043
jp.co.cyberagent.android.gpuimage.sample:dimen/abc_list_item_height_material = 0x7f050031
jp.co.cyberagent.android.gpuimage.sample:styleable/StateListDrawableItem = 0x7f0e0024
jp.co.cyberagent.android.gpuimage.sample:dimen/compat_button_inset_vertical_material = 0x7f05004f
jp.co.cyberagent.android.gpuimage.sample:color/tooltip_background_dark = 0x7f040055
jp.co.cyberagent.android.gpuimage.sample:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f0d00fc
jp.co.cyberagent.android.gpuimage.sample:attr/height = 0x7f02008a
jp.co.cyberagent.android.gpuimage.sample:attr/fontWeight = 0x7f020085
jp.co.cyberagent.android.gpuimage.sample:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f0d00bc
jp.co.cyberagent.android.gpuimage.sample:drawable/notification_bg_normal_pressed = 0x7f060068
jp.co.cyberagent.android.gpuimage.sample:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
