int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x7f01000c
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x7f01000d
int anim btn_checkbox_to_checked_icon_null_animation 0x7f01000e
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x7f01000f
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x7f010010
int anim btn_checkbox_to_unchecked_icon_null_animation 0x7f010011
int anim btn_radio_to_off_mtrl_dot_group_animation 0x7f010012
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x7f010013
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x7f010014
int anim btn_radio_to_on_mtrl_dot_group_animation 0x7f010015
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x7f010016
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x7f010017
int attr SharedValue 0x7f020000
int attr SharedValueId 0x7f020001
int attr actionBarDivider 0x7f020002
int attr actionBarItemBackground 0x7f020003
int attr actionBarPopupTheme 0x7f020004
int attr actionBarSize 0x7f020005
int attr actionBarSplitStyle 0x7f020006
int attr actionBarStyle 0x7f020007
int attr actionBarTabBarStyle 0x7f020008
int attr actionBarTabStyle 0x7f020009
int attr actionBarTabTextStyle 0x7f02000a
int attr actionBarTheme 0x7f02000b
int attr actionBarWidgetTheme 0x7f02000c
int attr actionButtonStyle 0x7f02000d
int attr actionDropDownStyle 0x7f02000e
int attr actionLayout 0x7f02000f
int attr actionMenuTextAppearance 0x7f020010
int attr actionMenuTextColor 0x7f020011
int attr actionModeBackground 0x7f020012
int attr actionModeCloseButtonStyle 0x7f020013
int attr actionModeCloseDrawable 0x7f020014
int attr actionModeCopyDrawable 0x7f020015
int attr actionModeCutDrawable 0x7f020016
int attr actionModeFindDrawable 0x7f020017
int attr actionModePasteDrawable 0x7f020018
int attr actionModePopupWindowStyle 0x7f020019
int attr actionModeSelectAllDrawable 0x7f02001a
int attr actionModeShareDrawable 0x7f02001b
int attr actionModeSplitBackground 0x7f02001c
int attr actionModeStyle 0x7f02001d
int attr actionModeWebSearchDrawable 0x7f02001e
int attr actionOverflowButtonStyle 0x7f02001f
int attr actionOverflowMenuStyle 0x7f020020
int attr actionProviderClass 0x7f020021
int attr actionViewClass 0x7f020022
int attr activityChooserViewStyle 0x7f020023
int attr alertDialogButtonGroupStyle 0x7f020024
int attr alertDialogCenterButtons 0x7f020025
int attr alertDialogStyle 0x7f020026
int attr alertDialogTheme 0x7f020027
int attr allowStacking 0x7f020028
int attr alpha 0x7f020029
int attr alphabeticModifiers 0x7f02002a
int attr altSrc 0x7f02002b
int attr animateCircleAngleTo 0x7f02002c
int attr animateRelativeTo 0x7f02002d
int attr applyMotionScene 0x7f02002e
int attr arcMode 0x7f02002f
int attr arrowHeadLength 0x7f020030
int attr arrowShaftLength 0x7f020031
int attr attributeName 0x7f020032
int attr autoCompleteMode 0x7f020033
int attr autoCompleteTextViewStyle 0x7f020034
int attr autoSizeMaxTextSize 0x7f020035
int attr autoSizeMinTextSize 0x7f020036
int attr autoSizePresetSizes 0x7f020037
int attr autoSizeStepGranularity 0x7f020038
int attr autoSizeTextType 0x7f020039
int attr autoTransition 0x7f02003a
int attr background 0x7f02003b
int attr backgroundSplit 0x7f02003c
int attr backgroundStacked 0x7f02003d
int attr backgroundTint 0x7f02003e
int attr backgroundTintMode 0x7f02003f
int attr barLength 0x7f020040
int attr barrierAllowsGoneWidgets 0x7f020041
int attr barrierDirection 0x7f020042
int attr barrierMargin 0x7f020043
int attr blendSrc 0x7f020044
int attr borderRound 0x7f020045
int attr borderRoundPercent 0x7f020046
int attr borderlessButtonStyle 0x7f020047
int attr brightness 0x7f020048
int attr buttonBarButtonStyle 0x7f020049
int attr buttonBarNegativeButtonStyle 0x7f02004a
int attr buttonBarNeutralButtonStyle 0x7f02004b
int attr buttonBarPositiveButtonStyle 0x7f02004c
int attr buttonBarStyle 0x7f02004d
int attr buttonCompat 0x7f02004e
int attr buttonGravity 0x7f02004f
int attr buttonIconDimen 0x7f020050
int attr buttonPanelSideLayout 0x7f020051
int attr buttonStyle 0x7f020052
int attr buttonStyleSmall 0x7f020053
int attr buttonTint 0x7f020054
int attr buttonTintMode 0x7f020055
int attr carousel_backwardTransition 0x7f020056
int attr carousel_emptyViewsBehavior 0x7f020057
int attr carousel_firstView 0x7f020058
int attr carousel_forwardTransition 0x7f020059
int attr carousel_infinite 0x7f02005a
int attr carousel_nextState 0x7f02005b
int attr carousel_previousState 0x7f02005c
int attr carousel_touchUpMode 0x7f02005d
int attr carousel_touchUp_dampeningFactor 0x7f02005e
int attr carousel_touchUp_velocityThreshold 0x7f02005f
int attr chainUseRtl 0x7f020060
int attr checkboxStyle 0x7f020061
int attr checkedTextViewStyle 0x7f020062
int attr circleRadius 0x7f020063
int attr circularflow_angles 0x7f020064
int attr circularflow_defaultAngle 0x7f020065
int attr circularflow_defaultRadius 0x7f020066
int attr circularflow_radiusInDP 0x7f020067
int attr circularflow_viewCenter 0x7f020068
int attr clearsTag 0x7f020069
int attr clickAction 0x7f02006a
int attr closeIcon 0x7f02006b
int attr closeItemLayout 0x7f02006c
int attr collapseContentDescription 0x7f02006d
int attr collapseIcon 0x7f02006e
int attr color 0x7f02006f
int attr colorAccent 0x7f020070
int attr colorBackgroundFloating 0x7f020071
int attr colorButtonNormal 0x7f020072
int attr colorControlActivated 0x7f020073
int attr colorControlHighlight 0x7f020074
int attr colorControlNormal 0x7f020075
int attr colorError 0x7f020076
int attr colorPrimary 0x7f020077
int attr colorPrimaryDark 0x7f020078
int attr colorSwitchThumbNormal 0x7f020079
int attr commitIcon 0x7f02007a
int attr constraintRotate 0x7f02007b
int attr constraintSet 0x7f02007c
int attr constraintSetEnd 0x7f02007d
int attr constraintSetStart 0x7f02007e
int attr constraint_referenced_ids 0x7f02007f
int attr constraint_referenced_tags 0x7f020080
int attr constraints 0x7f020081
int attr content 0x7f020082
int attr contentDescription 0x7f020083
int attr contentInsetEnd 0x7f020084
int attr contentInsetEndWithActions 0x7f020085
int attr contentInsetLeft 0x7f020086
int attr contentInsetRight 0x7f020087
int attr contentInsetStart 0x7f020088
int attr contentInsetStartWithNavigation 0x7f020089
int attr contrast 0x7f02008a
int attr controlBackground 0x7f02008b
int attr crossfade 0x7f02008c
int attr currentState 0x7f02008d
int attr curveFit 0x7f02008e
int attr customBoolean 0x7f02008f
int attr customColorDrawableValue 0x7f020090
int attr customColorValue 0x7f020091
int attr customDimension 0x7f020092
int attr customFloatValue 0x7f020093
int attr customIntegerValue 0x7f020094
int attr customNavigationLayout 0x7f020095
int attr customPixelDimension 0x7f020096
int attr customReference 0x7f020097
int attr customStringValue 0x7f020098
int attr defaultDuration 0x7f020099
int attr defaultQueryHint 0x7f02009a
int attr defaultState 0x7f02009b
int attr deltaPolarAngle 0x7f02009c
int attr deltaPolarRadius 0x7f02009d
int attr deriveConstraintsFrom 0x7f02009e
int attr dialogCornerRadius 0x7f02009f
int attr dialogPreferredPadding 0x7f0200a0
int attr dialogTheme 0x7f0200a1
int attr displayOptions 0x7f0200a2
int attr divider 0x7f0200a3
int attr dividerHorizontal 0x7f0200a4
int attr dividerPadding 0x7f0200a5
int attr dividerVertical 0x7f0200a6
int attr dragDirection 0x7f0200a7
int attr dragScale 0x7f0200a8
int attr dragThreshold 0x7f0200a9
int attr drawPath 0x7f0200aa
int attr drawableBottomCompat 0x7f0200ab
int attr drawableEndCompat 0x7f0200ac
int attr drawableLeftCompat 0x7f0200ad
int attr drawableRightCompat 0x7f0200ae
int attr drawableSize 0x7f0200af
int attr drawableStartCompat 0x7f0200b0
int attr drawableTint 0x7f0200b1
int attr drawableTintMode 0x7f0200b2
int attr drawableTopCompat 0x7f0200b3
int attr drawerArrowStyle 0x7f0200b4
int attr dropDownListViewStyle 0x7f0200b5
int attr dropdownListPreferredItemHeight 0x7f0200b6
int attr duration 0x7f0200b7
int attr editTextBackground 0x7f0200b8
int attr editTextColor 0x7f0200b9
int attr editTextStyle 0x7f0200ba
int attr elevation 0x7f0200bb
int attr expandActivityOverflowButtonDrawable 0x7f0200bc
int attr fastScrollEnabled 0x7f0200bd
int attr fastScrollHorizontalThumbDrawable 0x7f0200be
int attr fastScrollHorizontalTrackDrawable 0x7f0200bf
int attr fastScrollVerticalThumbDrawable 0x7f0200c0
int attr fastScrollVerticalTrackDrawable 0x7f0200c1
int attr firstBaselineToTopHeight 0x7f0200c2
int attr flow_firstHorizontalBias 0x7f0200c3
int attr flow_firstHorizontalStyle 0x7f0200c4
int attr flow_firstVerticalBias 0x7f0200c5
int attr flow_firstVerticalStyle 0x7f0200c6
int attr flow_horizontalAlign 0x7f0200c7
int attr flow_horizontalBias 0x7f0200c8
int attr flow_horizontalGap 0x7f0200c9
int attr flow_horizontalStyle 0x7f0200ca
int attr flow_lastHorizontalBias 0x7f0200cb
int attr flow_lastHorizontalStyle 0x7f0200cc
int attr flow_lastVerticalBias 0x7f0200cd
int attr flow_lastVerticalStyle 0x7f0200ce
int attr flow_maxElementsWrap 0x7f0200cf
int attr flow_padding 0x7f0200d0
int attr flow_verticalAlign 0x7f0200d1
int attr flow_verticalBias 0x7f0200d2
int attr flow_verticalGap 0x7f0200d3
int attr flow_verticalStyle 0x7f0200d4
int attr flow_wrapMode 0x7f0200d5
int attr font 0x7f0200d6
int attr fontFamily 0x7f0200d7
int attr fontProviderAuthority 0x7f0200d8
int attr fontProviderCerts 0x7f0200d9
int attr fontProviderFetchStrategy 0x7f0200da
int attr fontProviderFetchTimeout 0x7f0200db
int attr fontProviderPackage 0x7f0200dc
int attr fontProviderQuery 0x7f0200dd
int attr fontProviderSystemFontFamily 0x7f0200de
int attr fontStyle 0x7f0200df
int attr fontVariationSettings 0x7f0200e0
int attr fontWeight 0x7f0200e1
int attr framePosition 0x7f0200e2
int attr gapBetweenBars 0x7f0200e3
int attr goIcon 0x7f0200e4
int attr gpuimage_show_loading 0x7f0200e5
int attr gpuimage_surface_type 0x7f0200e6
int attr guidelineUseRtl 0x7f0200e7
int attr height 0x7f0200e8
int attr hideOnContentScroll 0x7f0200e9
int attr homeAsUpIndicator 0x7f0200ea
int attr homeLayout 0x7f0200eb
int attr icon 0x7f0200ec
int attr iconTint 0x7f0200ed
int attr iconTintMode 0x7f0200ee
int attr iconifiedByDefault 0x7f0200ef
int attr ifTagNotSet 0x7f0200f0
int attr ifTagSet 0x7f0200f1
int attr imageButtonStyle 0x7f0200f2
int attr imagePanX 0x7f0200f3
int attr imagePanY 0x7f0200f4
int attr imageRotate 0x7f0200f5
int attr imageZoom 0x7f0200f6
int attr indeterminateProgressStyle 0x7f0200f7
int attr initialActivityCount 0x7f0200f8
int attr isLightTheme 0x7f0200f9
int attr itemPadding 0x7f0200fa
int attr keyPositionType 0x7f0200fb
int attr lStar 0x7f0200fc
int attr lastBaselineToBottomHeight 0x7f0200fd
int attr layout 0x7f0200fe
int attr layoutDescription 0x7f0200ff
int attr layoutDuringTransition 0x7f020100
int attr layoutManager 0x7f020101
int attr layout_constrainedHeight 0x7f020102
int attr layout_constrainedWidth 0x7f020103
int attr layout_constraintBaseline_creator 0x7f020104
int attr layout_constraintBaseline_toBaselineOf 0x7f020105
int attr layout_constraintBaseline_toBottomOf 0x7f020106
int attr layout_constraintBaseline_toTopOf 0x7f020107
int attr layout_constraintBottom_creator 0x7f020108
int attr layout_constraintBottom_toBottomOf 0x7f020109
int attr layout_constraintBottom_toTopOf 0x7f02010a
int attr layout_constraintCircle 0x7f02010b
int attr layout_constraintCircleAngle 0x7f02010c
int attr layout_constraintCircleRadius 0x7f02010d
int attr layout_constraintDimensionRatio 0x7f02010e
int attr layout_constraintEnd_toEndOf 0x7f02010f
int attr layout_constraintEnd_toStartOf 0x7f020110
int attr layout_constraintGuide_begin 0x7f020111
int attr layout_constraintGuide_end 0x7f020112
int attr layout_constraintGuide_percent 0x7f020113
int attr layout_constraintHeight 0x7f020114
int attr layout_constraintHeight_default 0x7f020115
int attr layout_constraintHeight_max 0x7f020116
int attr layout_constraintHeight_min 0x7f020117
int attr layout_constraintHeight_percent 0x7f020118
int attr layout_constraintHorizontal_bias 0x7f020119
int attr layout_constraintHorizontal_chainStyle 0x7f02011a
int attr layout_constraintHorizontal_weight 0x7f02011b
int attr layout_constraintLeft_creator 0x7f02011c
int attr layout_constraintLeft_toLeftOf 0x7f02011d
int attr layout_constraintLeft_toRightOf 0x7f02011e
int attr layout_constraintRight_creator 0x7f02011f
int attr layout_constraintRight_toLeftOf 0x7f020120
int attr layout_constraintRight_toRightOf 0x7f020121
int attr layout_constraintStart_toEndOf 0x7f020122
int attr layout_constraintStart_toStartOf 0x7f020123
int attr layout_constraintTag 0x7f020124
int attr layout_constraintTop_creator 0x7f020125
int attr layout_constraintTop_toBottomOf 0x7f020126
int attr layout_constraintTop_toTopOf 0x7f020127
int attr layout_constraintVertical_bias 0x7f020128
int attr layout_constraintVertical_chainStyle 0x7f020129
int attr layout_constraintVertical_weight 0x7f02012a
int attr layout_constraintWidth 0x7f02012b
int attr layout_constraintWidth_default 0x7f02012c
int attr layout_constraintWidth_max 0x7f02012d
int attr layout_constraintWidth_min 0x7f02012e
int attr layout_constraintWidth_percent 0x7f02012f
int attr layout_editor_absoluteX 0x7f020130
int attr layout_editor_absoluteY 0x7f020131
int attr layout_goneMarginBaseline 0x7f020132
int attr layout_goneMarginBottom 0x7f020133
int attr layout_goneMarginEnd 0x7f020134
int attr layout_goneMarginLeft 0x7f020135
int attr layout_goneMarginRight 0x7f020136
int attr layout_goneMarginStart 0x7f020137
int attr layout_goneMarginTop 0x7f020138
int attr layout_marginBaseline 0x7f020139
int attr layout_optimizationLevel 0x7f02013a
int attr layout_wrapBehaviorInParent 0x7f02013b
int attr limitBoundsTo 0x7f02013c
int attr lineHeight 0x7f02013d
int attr listChoiceBackgroundIndicator 0x7f02013e
int attr listChoiceIndicatorMultipleAnimated 0x7f02013f
int attr listChoiceIndicatorSingleAnimated 0x7f020140
int attr listDividerAlertDialog 0x7f020141
int attr listItemLayout 0x7f020142
int attr listLayout 0x7f020143
int attr listMenuViewStyle 0x7f020144
int attr listPopupWindowStyle 0x7f020145
int attr listPreferredItemHeight 0x7f020146
int attr listPreferredItemHeightLarge 0x7f020147
int attr listPreferredItemHeightSmall 0x7f020148
int attr listPreferredItemPaddingEnd 0x7f020149
int attr listPreferredItemPaddingLeft 0x7f02014a
int attr listPreferredItemPaddingRight 0x7f02014b
int attr listPreferredItemPaddingStart 0x7f02014c
int attr logo 0x7f02014d
int attr logoDescription 0x7f02014e
int attr maxAcceleration 0x7f02014f
int attr maxButtonHeight 0x7f020150
int attr maxHeight 0x7f020151
int attr maxVelocity 0x7f020152
int attr maxWidth 0x7f020153
int attr measureWithLargestChild 0x7f020154
int attr menu 0x7f020155
int attr methodName 0x7f020156
int attr minHeight 0x7f020157
int attr minWidth 0x7f020158
int attr mock_diagonalsColor 0x7f020159
int attr mock_label 0x7f02015a
int attr mock_labelBackgroundColor 0x7f02015b
int attr mock_labelColor 0x7f02015c
int attr mock_showDiagonals 0x7f02015d
int attr mock_showLabel 0x7f02015e
int attr motionDebug 0x7f02015f
int attr motionEffect_alpha 0x7f020160
int attr motionEffect_end 0x7f020161
int attr motionEffect_move 0x7f020162
int attr motionEffect_start 0x7f020163
int attr motionEffect_strict 0x7f020164
int attr motionEffect_translationX 0x7f020165
int attr motionEffect_translationY 0x7f020166
int attr motionEffect_viewTransition 0x7f020167
int attr motionInterpolator 0x7f020168
int attr motionPathRotate 0x7f020169
int attr motionProgress 0x7f02016a
int attr motionStagger 0x7f02016b
int attr motionTarget 0x7f02016c
int attr motion_postLayoutCollision 0x7f02016d
int attr motion_triggerOnCollision 0x7f02016e
int attr moveWhenScrollAtTop 0x7f02016f
int attr multiChoiceItemLayout 0x7f020170
int attr navigationContentDescription 0x7f020171
int attr navigationIcon 0x7f020172
int attr navigationMode 0x7f020173
int attr nestedScrollFlags 0x7f020174
int attr nestedScrollViewStyle 0x7f020175
int attr numericModifiers 0x7f020176
int attr onCross 0x7f020177
int attr onHide 0x7f020178
int attr onNegativeCross 0x7f020179
int attr onPositiveCross 0x7f02017a
int attr onShow 0x7f02017b
int attr onStateTransition 0x7f02017c
int attr onTouchUp 0x7f02017d
int attr overlapAnchor 0x7f02017e
int attr overlay 0x7f02017f
int attr paddingBottomNoButtons 0x7f020180
int attr paddingEnd 0x7f020181
int attr paddingStart 0x7f020182
int attr paddingTopNoTitle 0x7f020183
int attr panelBackground 0x7f020184
int attr panelMenuListTheme 0x7f020185
int attr panelMenuListWidth 0x7f020186
int attr pathMotionArc 0x7f020187
int attr path_percent 0x7f020188
int attr percentHeight 0x7f020189
int attr percentWidth 0x7f02018a
int attr percentX 0x7f02018b
int attr percentY 0x7f02018c
int attr perpendicularPath_percent 0x7f02018d
int attr pivotAnchor 0x7f02018e
int attr placeholder_emptyVisibility 0x7f02018f
int attr polarRelativeTo 0x7f020190
int attr popupMenuStyle 0x7f020191
int attr popupTheme 0x7f020192
int attr popupWindowStyle 0x7f020193
int attr preserveIconSpacing 0x7f020194
int attr progressBarPadding 0x7f020195
int attr progressBarStyle 0x7f020196
int attr quantizeMotionInterpolator 0x7f020197
int attr quantizeMotionPhase 0x7f020198
int attr quantizeMotionSteps 0x7f020199
int attr queryBackground 0x7f02019a
int attr queryHint 0x7f02019b
int attr queryPatterns 0x7f02019c
int attr radioButtonStyle 0x7f02019d
int attr ratingBarStyle 0x7f02019e
int attr ratingBarStyleIndicator 0x7f02019f
int attr ratingBarStyleSmall 0x7f0201a0
int attr reactiveGuide_animateChange 0x7f0201a1
int attr reactiveGuide_applyToAllConstraintSets 0x7f0201a2
int attr reactiveGuide_applyToConstraintSet 0x7f0201a3
int attr reactiveGuide_valueId 0x7f0201a4
int attr recyclerViewStyle 0x7f0201a5
int attr region_heightLessThan 0x7f0201a6
int attr region_heightMoreThan 0x7f0201a7
int attr region_widthLessThan 0x7f0201a8
int attr region_widthMoreThan 0x7f0201a9
int attr reverseLayout 0x7f0201aa
int attr rotationCenterId 0x7f0201ab
int attr round 0x7f0201ac
int attr roundPercent 0x7f0201ad
int attr saturation 0x7f0201ae
int attr scaleFromTextSize 0x7f0201af
int attr searchHintIcon 0x7f0201b0
int attr searchIcon 0x7f0201b1
int attr searchViewStyle 0x7f0201b2
int attr seekBarStyle 0x7f0201b3
int attr selectableItemBackground 0x7f0201b4
int attr selectableItemBackgroundBorderless 0x7f0201b5
int attr setsTag 0x7f0201b6
int attr shortcutMatchRequired 0x7f0201b7
int attr showAsAction 0x7f0201b8
int attr showDividers 0x7f0201b9
int attr showPaths 0x7f0201ba
int attr showText 0x7f0201bb
int attr showTitle 0x7f0201bc
int attr singleChoiceItemLayout 0x7f0201bd
int attr sizePercent 0x7f0201be
int attr spanCount 0x7f0201bf
int attr spinBars 0x7f0201c0
int attr spinnerDropDownItemStyle 0x7f0201c1
int attr spinnerStyle 0x7f0201c2
int attr splitTrack 0x7f0201c3
int attr springBoundary 0x7f0201c4
int attr springDamping 0x7f0201c5
int attr springMass 0x7f0201c6
int attr springStiffness 0x7f0201c7
int attr springStopThreshold 0x7f0201c8
int attr srcCompat 0x7f0201c9
int attr stackFromEnd 0x7f0201ca
int attr staggered 0x7f0201cb
int attr state_above_anchor 0x7f0201cc
int attr subMenuArrow 0x7f0201cd
int attr submitBackground 0x7f0201ce
int attr subtitle 0x7f0201cf
int attr subtitleTextAppearance 0x7f0201d0
int attr subtitleTextColor 0x7f0201d1
int attr subtitleTextStyle 0x7f0201d2
int attr suggestionRowLayout 0x7f0201d3
int attr switchMinWidth 0x7f0201d4
int attr switchPadding 0x7f0201d5
int attr switchStyle 0x7f0201d6
int attr switchTextAppearance 0x7f0201d7
int attr targetId 0x7f0201d8
int attr telltales_tailColor 0x7f0201d9
int attr telltales_tailScale 0x7f0201da
int attr telltales_velocityMode 0x7f0201db
int attr textAllCaps 0x7f0201dc
int attr textAppearanceLargePopupMenu 0x7f0201dd
int attr textAppearanceListItem 0x7f0201de
int attr textAppearanceListItemSecondary 0x7f0201df
int attr textAppearanceListItemSmall 0x7f0201e0
int attr textAppearancePopupMenuHeader 0x7f0201e1
int attr textAppearanceSearchResultSubtitle 0x7f0201e2
int attr textAppearanceSearchResultTitle 0x7f0201e3
int attr textAppearanceSmallPopupMenu 0x7f0201e4
int attr textBackground 0x7f0201e5
int attr textBackgroundPanX 0x7f0201e6
int attr textBackgroundPanY 0x7f0201e7
int attr textBackgroundRotate 0x7f0201e8
int attr textBackgroundZoom 0x7f0201e9
int attr textColorAlertDialogListItem 0x7f0201ea
int attr textColorSearchUrl 0x7f0201eb
int attr textFillColor 0x7f0201ec
int attr textLocale 0x7f0201ed
int attr textOutlineColor 0x7f0201ee
int attr textOutlineThickness 0x7f0201ef
int attr textPanX 0x7f0201f0
int attr textPanY 0x7f0201f1
int attr textureBlurFactor 0x7f0201f2
int attr textureEffect 0x7f0201f3
int attr textureHeight 0x7f0201f4
int attr textureWidth 0x7f0201f5
int attr theme 0x7f0201f6
int attr thickness 0x7f0201f7
int attr thumbTextPadding 0x7f0201f8
int attr thumbTint 0x7f0201f9
int attr thumbTintMode 0x7f0201fa
int attr tickMark 0x7f0201fb
int attr tickMarkTint 0x7f0201fc
int attr tickMarkTintMode 0x7f0201fd
int attr tint 0x7f0201fe
int attr tintMode 0x7f0201ff
int attr title 0x7f020200
int attr titleMargin 0x7f020201
int attr titleMarginBottom 0x7f020202
int attr titleMarginEnd 0x7f020203
int attr titleMarginStart 0x7f020204
int attr titleMarginTop 0x7f020205
int attr titleMargins 0x7f020206
int attr titleTextAppearance 0x7f020207
int attr titleTextColor 0x7f020208
int attr titleTextStyle 0x7f020209
int attr toolbarNavigationButtonStyle 0x7f02020a
int attr toolbarStyle 0x7f02020b
int attr tooltipForegroundColor 0x7f02020c
int attr tooltipFrameBackground 0x7f02020d
int attr tooltipText 0x7f02020e
int attr touchAnchorId 0x7f02020f
int attr touchAnchorSide 0x7f020210
int attr touchRegionId 0x7f020211
int attr track 0x7f020212
int attr trackTint 0x7f020213
int attr trackTintMode 0x7f020214
int attr transformPivotTarget 0x7f020215
int attr transitionDisable 0x7f020216
int attr transitionEasing 0x7f020217
int attr transitionFlags 0x7f020218
int attr transitionPathRotate 0x7f020219
int attr triggerId 0x7f02021a
int attr triggerReceiver 0x7f02021b
int attr triggerSlack 0x7f02021c
int attr ttcIndex 0x7f02021d
int attr upDuration 0x7f02021e
int attr viewInflaterClass 0x7f02021f
int attr viewTransitionMode 0x7f020220
int attr viewTransitionOnCross 0x7f020221
int attr viewTransitionOnNegativeCross 0x7f020222
int attr viewTransitionOnPositiveCross 0x7f020223
int attr visibilityMode 0x7f020224
int attr voiceIcon 0x7f020225
int attr warmth 0x7f020226
int attr waveDecay 0x7f020227
int attr waveOffset 0x7f020228
int attr wavePeriod 0x7f020229
int attr wavePhase 0x7f02022a
int attr waveShape 0x7f02022b
int attr waveVariesBy 0x7f02022c
int attr windowActionBar 0x7f02022d
int attr windowActionBarOverlay 0x7f02022e
int attr windowActionModeOverlay 0x7f02022f
int attr windowFixedHeightMajor 0x7f020230
int attr windowFixedHeightMinor 0x7f020231
int attr windowFixedWidthMajor 0x7f020232
int attr windowFixedWidthMinor 0x7f020233
int attr windowMinWidthMajor 0x7f020234
int attr windowMinWidthMinor 0x7f020235
int attr windowNoTitle 0x7f020236
int bool abc_action_bar_embed_tabs 0x7f030000
int bool abc_allow_stacked_button_bar 0x7f030001
int bool abc_config_actionMenuItemAllCaps 0x7f030002
int color abc_background_cache_hint_selector_material_dark 0x7f040000
int color abc_background_cache_hint_selector_material_light 0x7f040001
int color abc_btn_colored_borderless_text_material 0x7f040002
int color abc_btn_colored_text_material 0x7f040003
int color abc_color_highlight_material 0x7f040004
int color abc_decor_view_status_guard 0x7f040005
int color abc_decor_view_status_guard_light 0x7f040006
int color abc_hint_foreground_material_dark 0x7f040007
int color abc_hint_foreground_material_light 0x7f040008
int color abc_primary_text_disable_only_material_dark 0x7f040009
int color abc_primary_text_disable_only_material_light 0x7f04000a
int color abc_primary_text_material_dark 0x7f04000b
int color abc_primary_text_material_light 0x7f04000c
int color abc_search_url_text 0x7f04000d
int color abc_search_url_text_normal 0x7f04000e
int color abc_search_url_text_pressed 0x7f04000f
int color abc_search_url_text_selected 0x7f040010
int color abc_secondary_text_material_dark 0x7f040011
int color abc_secondary_text_material_light 0x7f040012
int color abc_tint_btn_checkable 0x7f040013
int color abc_tint_default 0x7f040014
int color abc_tint_edittext 0x7f040015
int color abc_tint_seek_thumb 0x7f040016
int color abc_tint_spinner 0x7f040017
int color abc_tint_switch_track 0x7f040018
int color accent_material_dark 0x7f040019
int color accent_material_light 0x7f04001a
int color androidx_core_ripple_material_light 0x7f04001b
int color androidx_core_secondary_text_default_material_light 0x7f04001c
int color background_floating_material_dark 0x7f04001d
int color background_floating_material_light 0x7f04001e
int color background_material_dark 0x7f04001f
int color background_material_light 0x7f040020
int color bright_foreground_disabled_material_dark 0x7f040021
int color bright_foreground_disabled_material_light 0x7f040022
int color bright_foreground_inverse_material_dark 0x7f040023
int color bright_foreground_inverse_material_light 0x7f040024
int color bright_foreground_material_dark 0x7f040025
int color bright_foreground_material_light 0x7f040026
int color button_material_dark 0x7f040027
int color button_material_light 0x7f040028
int color colorAccent 0x7f040029
int color dim_foreground_disabled_material_dark 0x7f04002a
int color dim_foreground_disabled_material_light 0x7f04002b
int color dim_foreground_material_dark 0x7f04002c
int color dim_foreground_material_light 0x7f04002d
int color error_color_material_dark 0x7f04002e
int color error_color_material_light 0x7f04002f
int color foreground_material_dark 0x7f040030
int color foreground_material_light 0x7f040031
int color highlighted_text_material_dark 0x7f040032
int color highlighted_text_material_light 0x7f040033
int color material_blue_grey_800 0x7f040034
int color material_blue_grey_900 0x7f040035
int color material_blue_grey_950 0x7f040036
int color material_deep_teal_200 0x7f040037
int color material_deep_teal_500 0x7f040038
int color material_grey_100 0x7f040039
int color material_grey_300 0x7f04003a
int color material_grey_50 0x7f04003b
int color material_grey_600 0x7f04003c
int color material_grey_800 0x7f04003d
int color material_grey_850 0x7f04003e
int color material_grey_900 0x7f04003f
int color notification_action_color_filter 0x7f040040
int color notification_icon_bg_color 0x7f040041
int color primary_dark_material_dark 0x7f040042
int color primary_dark_material_light 0x7f040043
int color primary_material_dark 0x7f040044
int color primary_material_light 0x7f040045
int color primary_text_default_material_dark 0x7f040046
int color primary_text_default_material_light 0x7f040047
int color primary_text_disabled_material_dark 0x7f040048
int color primary_text_disabled_material_light 0x7f040049
int color ripple_material_dark 0x7f04004a
int color ripple_material_light 0x7f04004b
int color secondary_text_default_material_dark 0x7f04004c
int color secondary_text_default_material_light 0x7f04004d
int color secondary_text_disabled_material_dark 0x7f04004e
int color secondary_text_disabled_material_light 0x7f04004f
int color switch_thumb_disabled_material_dark 0x7f040050
int color switch_thumb_disabled_material_light 0x7f040051
int color switch_thumb_material_dark 0x7f040052
int color switch_thumb_material_light 0x7f040053
int color switch_thumb_normal_material_dark 0x7f040054
int color switch_thumb_normal_material_light 0x7f040055
int color tooltip_background_dark 0x7f040056
int color tooltip_background_light 0x7f040057
int dimen abc_action_bar_content_inset_material 0x7f050000
int dimen abc_action_bar_content_inset_with_nav 0x7f050001
int dimen abc_action_bar_default_height_material 0x7f050002
int dimen abc_action_bar_default_padding_end_material 0x7f050003
int dimen abc_action_bar_default_padding_start_material 0x7f050004
int dimen abc_action_bar_elevation_material 0x7f050005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f050006
int dimen abc_action_bar_overflow_padding_end_material 0x7f050007
int dimen abc_action_bar_overflow_padding_start_material 0x7f050008
int dimen abc_action_bar_stacked_max_height 0x7f050009
int dimen abc_action_bar_stacked_tab_max_width 0x7f05000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f05000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f05000c
int dimen abc_action_button_min_height_material 0x7f05000d
int dimen abc_action_button_min_width_material 0x7f05000e
int dimen abc_action_button_min_width_overflow_material 0x7f05000f
int dimen abc_alert_dialog_button_bar_height 0x7f050010
int dimen abc_alert_dialog_button_dimen 0x7f050011
int dimen abc_button_inset_horizontal_material 0x7f050012
int dimen abc_button_inset_vertical_material 0x7f050013
int dimen abc_button_padding_horizontal_material 0x7f050014
int dimen abc_button_padding_vertical_material 0x7f050015
int dimen abc_cascading_menus_min_smallest_width 0x7f050016
int dimen abc_config_prefDialogWidth 0x7f050017
int dimen abc_control_corner_material 0x7f050018
int dimen abc_control_inset_material 0x7f050019
int dimen abc_control_padding_material 0x7f05001a
int dimen abc_dialog_corner_radius_material 0x7f05001b
int dimen abc_dialog_fixed_height_major 0x7f05001c
int dimen abc_dialog_fixed_height_minor 0x7f05001d
int dimen abc_dialog_fixed_width_major 0x7f05001e
int dimen abc_dialog_fixed_width_minor 0x7f05001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f050020
int dimen abc_dialog_list_padding_top_no_title 0x7f050021
int dimen abc_dialog_min_width_major 0x7f050022
int dimen abc_dialog_min_width_minor 0x7f050023
int dimen abc_dialog_padding_material 0x7f050024
int dimen abc_dialog_padding_top_material 0x7f050025
int dimen abc_dialog_title_divider_material 0x7f050026
int dimen abc_disabled_alpha_material_dark 0x7f050027
int dimen abc_disabled_alpha_material_light 0x7f050028
int dimen abc_dropdownitem_icon_width 0x7f050029
int dimen abc_dropdownitem_text_padding_left 0x7f05002a
int dimen abc_dropdownitem_text_padding_right 0x7f05002b
int dimen abc_edit_text_inset_bottom_material 0x7f05002c
int dimen abc_edit_text_inset_horizontal_material 0x7f05002d
int dimen abc_edit_text_inset_top_material 0x7f05002e
int dimen abc_floating_window_z 0x7f05002f
int dimen abc_list_item_height_large_material 0x7f050030
int dimen abc_list_item_height_material 0x7f050031
int dimen abc_list_item_height_small_material 0x7f050032
int dimen abc_list_item_padding_horizontal_material 0x7f050033
int dimen abc_panel_menu_list_width 0x7f050034
int dimen abc_progress_bar_height_material 0x7f050035
int dimen abc_search_view_preferred_height 0x7f050036
int dimen abc_search_view_preferred_width 0x7f050037
int dimen abc_seekbar_track_background_height_material 0x7f050038
int dimen abc_seekbar_track_progress_height_material 0x7f050039
int dimen abc_select_dialog_padding_start_material 0x7f05003a
int dimen abc_switch_padding 0x7f05003b
int dimen abc_text_size_body_1_material 0x7f05003c
int dimen abc_text_size_body_2_material 0x7f05003d
int dimen abc_text_size_button_material 0x7f05003e
int dimen abc_text_size_caption_material 0x7f05003f
int dimen abc_text_size_display_1_material 0x7f050040
int dimen abc_text_size_display_2_material 0x7f050041
int dimen abc_text_size_display_3_material 0x7f050042
int dimen abc_text_size_display_4_material 0x7f050043
int dimen abc_text_size_headline_material 0x7f050044
int dimen abc_text_size_large_material 0x7f050045
int dimen abc_text_size_medium_material 0x7f050046
int dimen abc_text_size_menu_header_material 0x7f050047
int dimen abc_text_size_menu_material 0x7f050048
int dimen abc_text_size_small_material 0x7f050049
int dimen abc_text_size_subhead_material 0x7f05004a
int dimen abc_text_size_subtitle_material_toolbar 0x7f05004b
int dimen abc_text_size_title_material 0x7f05004c
int dimen abc_text_size_title_material_toolbar 0x7f05004d
int dimen compat_button_inset_horizontal_material 0x7f05004e
int dimen compat_button_inset_vertical_material 0x7f05004f
int dimen compat_button_padding_horizontal_material 0x7f050050
int dimen compat_button_padding_vertical_material 0x7f050051
int dimen compat_control_corner_material 0x7f050052
int dimen compat_notification_large_icon_max_height 0x7f050053
int dimen compat_notification_large_icon_max_width 0x7f050054
int dimen disabled_alpha_material_dark 0x7f050055
int dimen disabled_alpha_material_light 0x7f050056
int dimen fastscroll_default_thickness 0x7f050057
int dimen fastscroll_margin 0x7f050058
int dimen fastscroll_minimum_range 0x7f050059
int dimen highlight_alpha_material_colored 0x7f05005a
int dimen highlight_alpha_material_dark 0x7f05005b
int dimen highlight_alpha_material_light 0x7f05005c
int dimen hint_alpha_material_dark 0x7f05005d
int dimen hint_alpha_material_light 0x7f05005e
int dimen hint_pressed_alpha_material_dark 0x7f05005f
int dimen hint_pressed_alpha_material_light 0x7f050060
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f050061
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f050062
int dimen item_touch_helper_swipe_escape_velocity 0x7f050063
int dimen notification_action_icon_size 0x7f050064
int dimen notification_action_text_size 0x7f050065
int dimen notification_big_circle_margin 0x7f050066
int dimen notification_content_margin_start 0x7f050067
int dimen notification_large_icon_height 0x7f050068
int dimen notification_large_icon_width 0x7f050069
int dimen notification_main_column_padding_top 0x7f05006a
int dimen notification_media_narrow_margin 0x7f05006b
int dimen notification_right_icon_size 0x7f05006c
int dimen notification_right_side_padding_top 0x7f05006d
int dimen notification_small_icon_background_padding 0x7f05006e
int dimen notification_small_icon_size_as_large 0x7f05006f
int dimen notification_subtext_size 0x7f050070
int dimen notification_top_pad 0x7f050071
int dimen notification_top_pad_large_text 0x7f050072
int dimen param_item_margin_bottom 0x7f050073
int dimen tooltip_corner_radius 0x7f050074
int dimen tooltip_horizontal_padding 0x7f050075
int dimen tooltip_margin 0x7f050076
int dimen tooltip_precise_anchor_extra_offset 0x7f050077
int dimen tooltip_precise_anchor_threshold 0x7f050078
int dimen tooltip_vertical_padding 0x7f050079
int dimen tooltip_y_offset_non_touch 0x7f05007a
int dimen tooltip_y_offset_touch 0x7f05007b
int drawable abc_ab_share_pack_mtrl_alpha 0x7f060000
int drawable abc_action_bar_item_background_material 0x7f060001
int drawable abc_btn_borderless_material 0x7f060002
int drawable abc_btn_check_material 0x7f060003
int drawable abc_btn_check_material_anim 0x7f060004
int drawable abc_btn_check_to_on_mtrl_000 0x7f060005
int drawable abc_btn_check_to_on_mtrl_015 0x7f060006
int drawable abc_btn_colored_material 0x7f060007
int drawable abc_btn_default_mtrl_shape 0x7f060008
int drawable abc_btn_radio_material 0x7f060009
int drawable abc_btn_radio_material_anim 0x7f06000a
int drawable abc_btn_radio_to_on_mtrl_000 0x7f06000b
int drawable abc_btn_radio_to_on_mtrl_015 0x7f06000c
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f06000d
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f06000e
int drawable abc_cab_background_internal_bg 0x7f06000f
int drawable abc_cab_background_top_material 0x7f060010
int drawable abc_cab_background_top_mtrl_alpha 0x7f060011
int drawable abc_control_background_material 0x7f060012
int drawable abc_dialog_material_background 0x7f060013
int drawable abc_edit_text_material 0x7f060014
int drawable abc_ic_ab_back_material 0x7f060015
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f060016
int drawable abc_ic_clear_material 0x7f060017
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f060018
int drawable abc_ic_go_search_api_material 0x7f060019
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f06001a
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f06001b
int drawable abc_ic_menu_overflow_material 0x7f06001c
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f06001d
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f06001e
int drawable abc_ic_menu_share_mtrl_alpha 0x7f06001f
int drawable abc_ic_search_api_material 0x7f060020
int drawable abc_ic_star_black_16dp 0x7f060021
int drawable abc_ic_star_black_36dp 0x7f060022
int drawable abc_ic_star_black_48dp 0x7f060023
int drawable abc_ic_star_half_black_16dp 0x7f060024
int drawable abc_ic_star_half_black_36dp 0x7f060025
int drawable abc_ic_star_half_black_48dp 0x7f060026
int drawable abc_ic_voice_search_api_material 0x7f060027
int drawable abc_item_background_holo_dark 0x7f060028
int drawable abc_item_background_holo_light 0x7f060029
int drawable abc_list_divider_material 0x7f06002a
int drawable abc_list_divider_mtrl_alpha 0x7f06002b
int drawable abc_list_focused_holo 0x7f06002c
int drawable abc_list_longpressed_holo 0x7f06002d
int drawable abc_list_pressed_holo_dark 0x7f06002e
int drawable abc_list_pressed_holo_light 0x7f06002f
int drawable abc_list_selector_background_transition_holo_dark 0x7f060030
int drawable abc_list_selector_background_transition_holo_light 0x7f060031
int drawable abc_list_selector_disabled_holo_dark 0x7f060032
int drawable abc_list_selector_disabled_holo_light 0x7f060033
int drawable abc_list_selector_holo_dark 0x7f060034
int drawable abc_list_selector_holo_light 0x7f060035
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f060036
int drawable abc_popup_background_mtrl_mult 0x7f060037
int drawable abc_ratingbar_indicator_material 0x7f060038
int drawable abc_ratingbar_material 0x7f060039
int drawable abc_ratingbar_small_material 0x7f06003a
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f06003b
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f06003c
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f06003d
int drawable abc_scrubber_primary_mtrl_alpha 0x7f06003e
int drawable abc_scrubber_track_mtrl_alpha 0x7f06003f
int drawable abc_seekbar_thumb_material 0x7f060040
int drawable abc_seekbar_tick_mark_material 0x7f060041
int drawable abc_seekbar_track_material 0x7f060042
int drawable abc_spinner_mtrl_am_alpha 0x7f060043
int drawable abc_spinner_textfield_background_material 0x7f060044
int drawable abc_switch_thumb_material 0x7f060045
int drawable abc_switch_track_mtrl_alpha 0x7f060046
int drawable abc_tab_indicator_material 0x7f060047
int drawable abc_tab_indicator_mtrl_alpha 0x7f060048
int drawable abc_text_cursor_material 0x7f060049
int drawable abc_text_select_handle_left_mtrl_dark 0x7f06004a
int drawable abc_text_select_handle_left_mtrl_light 0x7f06004b
int drawable abc_text_select_handle_middle_mtrl_dark 0x7f06004c
int drawable abc_text_select_handle_middle_mtrl_light 0x7f06004d
int drawable abc_text_select_handle_right_mtrl_dark 0x7f06004e
int drawable abc_text_select_handle_right_mtrl_light 0x7f06004f
int drawable abc_textfield_activated_mtrl_alpha 0x7f060050
int drawable abc_textfield_default_mtrl_alpha 0x7f060051
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f060052
int drawable abc_textfield_search_default_mtrl_alpha 0x7f060053
int drawable abc_textfield_search_material 0x7f060054
int drawable abc_vector_test 0x7f060055
int drawable btn_checkbox_checked_mtrl 0x7f060056
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x7f060057
int drawable btn_checkbox_unchecked_mtrl 0x7f060058
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x7f060059
int drawable btn_radio_off_mtrl 0x7f06005a
int drawable btn_radio_off_to_on_mtrl_animation 0x7f06005b
int drawable btn_radio_on_mtrl 0x7f06005c
int drawable btn_radio_on_to_off_mtrl_animation 0x7f06005d
int drawable ic_action_search 0x7f06005e
int drawable ic_launcher 0x7f06005f
int drawable ic_switch_camera 0x7f060060
int drawable lookup_amatorka 0x7f060061
int drawable notification_action_background 0x7f060062
int drawable notification_bg 0x7f060063
int drawable notification_bg_low 0x7f060064
int drawable notification_bg_low_normal 0x7f060065
int drawable notification_bg_low_pressed 0x7f060066
int drawable notification_bg_normal 0x7f060067
int drawable notification_bg_normal_pressed 0x7f060068
int drawable notification_icon_background 0x7f060069
int drawable notification_template_icon_bg 0x7f06006a
int drawable notification_template_icon_low_bg 0x7f06006b
int drawable notification_tile_bg 0x7f06006c
int drawable notify_panel_notification_icon_bg 0x7f06006d
int drawable tooltip_frame_dark 0x7f06006e
int drawable tooltip_frame_light 0x7f06006f
int id ALT 0x7f070000
int id CTRL 0x7f070001
int id FUNCTION 0x7f070002
int id META 0x7f070003
int id NO_DEBUG 0x7f070004
int id SHIFT 0x7f070005
int id SHOW_ALL 0x7f070006
int id SHOW_PATH 0x7f070007
int id SHOW_PROGRESS 0x7f070008
int id SYM 0x7f070009
int id accelerate 0x7f07000a
int id accessibility_action_clickable_span 0x7f07000b
int id accessibility_custom_action_0 0x7f07000c
int id accessibility_custom_action_1 0x7f07000d
int id accessibility_custom_action_10 0x7f07000e
int id accessibility_custom_action_11 0x7f07000f
int id accessibility_custom_action_12 0x7f070010
int id accessibility_custom_action_13 0x7f070011
int id accessibility_custom_action_14 0x7f070012
int id accessibility_custom_action_15 0x7f070013
int id accessibility_custom_action_16 0x7f070014
int id accessibility_custom_action_17 0x7f070015
int id accessibility_custom_action_18 0x7f070016
int id accessibility_custom_action_19 0x7f070017
int id accessibility_custom_action_2 0x7f070018
int id accessibility_custom_action_20 0x7f070019
int id accessibility_custom_action_21 0x7f07001a
int id accessibility_custom_action_22 0x7f07001b
int id accessibility_custom_action_23 0x7f07001c
int id accessibility_custom_action_24 0x7f07001d
int id accessibility_custom_action_25 0x7f07001e
int id accessibility_custom_action_26 0x7f07001f
int id accessibility_custom_action_27 0x7f070020
int id accessibility_custom_action_28 0x7f070021
int id accessibility_custom_action_29 0x7f070022
int id accessibility_custom_action_3 0x7f070023
int id accessibility_custom_action_30 0x7f070024
int id accessibility_custom_action_31 0x7f070025
int id accessibility_custom_action_4 0x7f070026
int id accessibility_custom_action_5 0x7f070027
int id accessibility_custom_action_6 0x7f070028
int id accessibility_custom_action_7 0x7f070029
int id accessibility_custom_action_8 0x7f07002a
int id accessibility_custom_action_9 0x7f07002b
int id actionDown 0x7f07002c
int id actionDownUp 0x7f07002d
int id actionUp 0x7f07002e
int id action_bar 0x7f07002f
int id action_bar_activity_content 0x7f070030
int id action_bar_container 0x7f070031
int id action_bar_root 0x7f070032
int id action_bar_spinner 0x7f070033
int id action_bar_subtitle 0x7f070034
int id action_bar_title 0x7f070035
int id action_container 0x7f070036
int id action_context_bar 0x7f070037
int id action_divider 0x7f070038
int id action_image 0x7f070039
int id action_menu_divider 0x7f07003a
int id action_menu_presenter 0x7f07003b
int id action_mode_bar 0x7f07003c
int id action_mode_bar_stub 0x7f07003d
int id action_mode_close_button 0x7f07003e
int id action_text 0x7f07003f
int id actions 0x7f070040
int id activity_chooser_view_content 0x7f070041
int id add 0x7f070042
int id alertTitle 0x7f070043
int id aligned 0x7f070044
int id allStates 0x7f070045
int id always 0x7f070046
int id animateToEnd 0x7f070047
int id animateToStart 0x7f070048
int id antiClockwise 0x7f070049
int id anticipate 0x7f07004a
int id asConfigured 0x7f07004b
int id async 0x7f07004c
int id auto 0x7f07004d
int id autoComplete 0x7f07004e
int id autoCompleteToEnd 0x7f07004f
int id autoCompleteToStart 0x7f070050
int id bar 0x7f070051
int id barrier 0x7f070052
int id baseline 0x7f070053
int id beginOnFirstDraw 0x7f070054
int id beginning 0x7f070055
int id bestChoice 0x7f070056
int id blocking 0x7f070057
int id bottom 0x7f070058
int id bottom_panel 0x7f070059
int id bounce 0x7f07005a
int id bounceBoth 0x7f07005b
int id bounceEnd 0x7f07005c
int id bounceStart 0x7f07005d
int id buttonPanel 0x7f07005e
int id button_camera 0x7f07005f
int id button_capture 0x7f070060
int id button_choose_filter 0x7f070061
int id button_gallery 0x7f070062
int id button_save 0x7f070063
int id cache_measures 0x7f070064
int id callMeasure 0x7f070065
int id carryVelocity 0x7f070066
int id category_indicator_view 0x7f070067
int id category_name_text_view 0x7f070068
int id center 0x7f070069
int id center_vertical 0x7f07006a
int id chain 0x7f07006b
int id chain2 0x7f07006c
int id chains 0x7f07006d
int id checkbox 0x7f07006e
int id checked 0x7f07006f
int id chronometer 0x7f070070
int id clockwise 0x7f070071
int id closest 0x7f070072
int id collapseActionView 0x7f070073
int id constraint 0x7f070074
int id content 0x7f070075
int id contentPanel 0x7f070076
int id continuousVelocity 0x7f070077
int id cos 0x7f070078
int id currentState 0x7f070079
int id custom 0x7f07007a
int id customPanel 0x7f07007b
int id decelerate 0x7f07007c
int id decelerateAndComplete 0x7f07007d
int id decor_content_parent 0x7f07007e
int id default_activity_button 0x7f07007f
int id deltaRelative 0x7f070080
int id dependency_ordering 0x7f070081
int id dialog_button 0x7f070082
int id dimensions 0x7f070083
int id direct 0x7f070084
int id disableHome 0x7f070085
int id disableIntraAutoTransition 0x7f070086
int id disablePostScroll 0x7f070087
int id disableScroll 0x7f070088
int id dragAnticlockwise 0x7f070089
int id dragClockwise 0x7f07008a
int id dragDown 0x7f07008b
int id dragEnd 0x7f07008c
int id dragLeft 0x7f07008d
int id dragRight 0x7f07008e
int id dragStart 0x7f07008f
int id dragUp 0x7f070090
int id easeIn 0x7f070091
int id easeInOut 0x7f070092
int id easeOut 0x7f070093
int id east 0x7f070094
int id edit_query 0x7f070095
int id end 0x7f070096
int id expand_activities_button 0x7f070097
int id expanded_menu 0x7f070098
int id filter_category_recyclerview 0x7f070099
int id filter_list_recyclerview 0x7f07009a
int id filter_name_text_view 0x7f07009b
int id flip 0x7f07009c
int id forever 0x7f07009d
int id frost 0x7f07009e
int id gone 0x7f07009f
int id gpuimage 0x7f0700a0
int id graph 0x7f0700a1
int id graph_wrap 0x7f0700a2
int id group_divider 0x7f0700a3
int id grouping 0x7f0700a4
int id groups 0x7f0700a5
int id home 0x7f0700a6
int id homeAsUp 0x7f0700a7
int id honorRequest 0x7f0700a8
int id horizontal_only 0x7f0700a9
int id icon 0x7f0700aa
int id icon_group 0x7f0700ab
int id ifRoom 0x7f0700ac
int id ignore 0x7f0700ad
int id ignoreRequest 0x7f0700ae
int id image 0x7f0700af
int id img_switch_camera 0x7f0700b0
int id immediateStop 0x7f0700b1
int id included 0x7f0700b2
int id info 0x7f0700b3
int id invisible 0x7f0700b4
int id is_pooling_container_tag 0x7f0700b5
int id italic 0x7f0700b6
int id item1 0x7f0700b7
int id item2 0x7f0700b8
int id item3 0x7f0700b9
int id item4 0x7f0700ba
int id item_touch_helper_previous_elevation 0x7f0700bb
int id jumpToEnd 0x7f0700bc
int id jumpToStart 0x7f0700bd
int id layout 0x7f0700be
int id left 0x7f0700bf
int id legacy 0x7f0700c0
int id line1 0x7f0700c1
int id line3 0x7f0700c2
int id linear 0x7f0700c3
int id listMode 0x7f0700c4
int id list_item 0x7f0700c5
int id match_constraint 0x7f0700c6
int id match_parent 0x7f0700c7
int id message 0x7f0700c8
int id middle 0x7f0700c9
int id motion_base 0x7f0700ca
int id multiply 0x7f0700cb
int id never 0x7f0700cc
int id neverCompleteToEnd 0x7f0700cd
int id neverCompleteToStart 0x7f0700ce
int id noState 0x7f0700cf
int id none 0x7f0700d0
int id normal 0x7f0700d1
int id north 0x7f0700d2
int id notification_background 0x7f0700d3
int id notification_main_column 0x7f0700d4
int id notification_main_column_container 0x7f0700d5
int id off 0x7f0700d6
int id on 0x7f0700d7
int id onInterceptTouchReturnSwipe 0x7f0700d8
int id overshoot 0x7f0700d9
int id packed 0x7f0700da
int id parameter_adjustment_container 0x7f0700db
int id parent 0x7f0700dc
int id parentPanel 0x7f0700dd
int id parentRelative 0x7f0700de
int id path 0x7f0700df
int id pathRelative 0x7f0700e0
int id percent 0x7f0700e1
int id pooling_container_listener_holder_tag 0x7f0700e2
int id position 0x7f0700e3
int id postLayout 0x7f0700e4
int id progress_circular 0x7f0700e5
int id progress_horizontal 0x7f0700e6
int id radio 0x7f0700e7
int id ratio 0x7f0700e8
int id rectangles 0x7f0700e9
int id reverseSawtooth 0x7f0700ea
int id right 0x7f0700eb
int id right_icon 0x7f0700ec
int id right_side 0x7f0700ed
int id sawtooth 0x7f0700ee
int id screen 0x7f0700ef
int id scrollIndicatorDown 0x7f0700f0
int id scrollIndicatorUp 0x7f0700f1
int id scrollView 0x7f0700f2
int id search_badge 0x7f0700f3
int id search_bar 0x7f0700f4
int id search_button 0x7f0700f5
int id search_close_btn 0x7f0700f6
int id search_edit_frame 0x7f0700f7
int id search_go_btn 0x7f0700f8
int id search_mag_icon 0x7f0700f9
int id search_plate 0x7f0700fa
int id search_src_text 0x7f0700fb
int id search_voice_btn 0x7f0700fc
int id seekBar 0x7f0700fd
int id select_dialog_listview 0x7f0700fe
int id sharedValueSet 0x7f0700ff
int id sharedValueUnset 0x7f070100
int id shortcut 0x7f070101
int id showCustom 0x7f070102
int id showHome 0x7f070103
int id showTitle 0x7f070104
int id sin 0x7f070105
int id skipped 0x7f070106
int id south 0x7f070107
int id spacer 0x7f070108
int id spline 0x7f070109
int id split_action_bar 0x7f07010a
int id spread 0x7f07010b
int id spread_inside 0x7f07010c
int id spring 0x7f07010d
int id square 0x7f07010e
int id src_atop 0x7f07010f
int id src_in 0x7f070110
int id src_over 0x7f070111
int id standard 0x7f070112
int id start 0x7f070113
int id startHorizontal 0x7f070114
int id startVertical 0x7f070115
int id staticLayout 0x7f070116
int id staticPostLayout 0x7f070117
int id stop 0x7f070118
int id submenuarrow 0x7f070119
int id submit_area 0x7f07011a
int id supportScrollUp 0x7f07011b
int id surfaceView 0x7f07011c
int id surface_view 0x7f07011d
int id tabMode 0x7f07011e
int id tag_accessibility_actions 0x7f07011f
int id tag_accessibility_clickable_spans 0x7f070120
int id tag_accessibility_heading 0x7f070121
int id tag_accessibility_pane_title 0x7f070122
int id tag_on_apply_window_listener 0x7f070123
int id tag_on_receive_content_listener 0x7f070124
int id tag_on_receive_content_mime_types 0x7f070125
int id tag_screen_reader_focusable 0x7f070126
int id tag_state_description 0x7f070127
int id tag_transition_group 0x7f070128
int id tag_unhandled_key_event_manager 0x7f070129
int id tag_unhandled_key_listeners 0x7f07012a
int id tag_window_insets_animation_callback 0x7f07012b
int id text 0x7f07012c
int id text2 0x7f07012d
int id textSpacerNoButtons 0x7f07012e
int id textSpacerNoTitle 0x7f07012f
int id texture_view 0x7f070130
int id time 0x7f070131
int id title 0x7f070132
int id titleDividerNoCustom 0x7f070133
int id title_template 0x7f070134
int id toggle 0x7f070135
int id top 0x7f070136
int id topPanel 0x7f070137
int id transitionToEnd 0x7f070138
int id transitionToStart 0x7f070139
int id triangle 0x7f07013a
int id unchecked 0x7f07013b
int id uniform 0x7f07013c
int id up 0x7f07013d
int id useLogo 0x7f07013e
int id vertical_only 0x7f07013f
int id view_transition 0x7f070140
int id view_tree_lifecycle_owner 0x7f070141
int id visible 0x7f070142
int id west 0x7f070143
int id withText 0x7f070144
int id wrap 0x7f070145
int id wrap_content 0x7f070146
int id wrap_content_constrained 0x7f070147
int id x_left 0x7f070148
int id x_right 0x7f070149
int integer abc_config_activityDefaultDur 0x7f080000
int integer abc_config_activityShortDur 0x7f080001
int integer cancel_button_image_alpha 0x7f080002
int integer config_tooltipAnimTime 0x7f080003
int integer status_bar_notification_info_maxnum 0x7f080004
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x7f090000
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x7f090001
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x7f090002
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x7f090003
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x7f090004
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x7f090005
int interpolator fast_out_slow_in 0x7f090006
int layout abc_action_bar_title_item 0x7f0a0000
int layout abc_action_bar_up_container 0x7f0a0001
int layout abc_action_menu_item_layout 0x7f0a0002
int layout abc_action_menu_layout 0x7f0a0003
int layout abc_action_mode_bar 0x7f0a0004
int layout abc_action_mode_close_item_material 0x7f0a0005
int layout abc_activity_chooser_view 0x7f0a0006
int layout abc_activity_chooser_view_list_item 0x7f0a0007
int layout abc_alert_dialog_button_bar_material 0x7f0a0008
int layout abc_alert_dialog_material 0x7f0a0009
int layout abc_alert_dialog_title_material 0x7f0a000a
int layout abc_cascading_menu_item_layout 0x7f0a000b
int layout abc_dialog_title_material 0x7f0a000c
int layout abc_expanded_menu_layout 0x7f0a000d
int layout abc_list_menu_item_checkbox 0x7f0a000e
int layout abc_list_menu_item_icon 0x7f0a000f
int layout abc_list_menu_item_layout 0x7f0a0010
int layout abc_list_menu_item_radio 0x7f0a0011
int layout abc_popup_menu_header_item_layout 0x7f0a0012
int layout abc_popup_menu_item_layout 0x7f0a0013
int layout abc_screen_content_include 0x7f0a0014
int layout abc_screen_simple 0x7f0a0015
int layout abc_screen_simple_overlay_action_mode 0x7f0a0016
int layout abc_screen_toolbar 0x7f0a0017
int layout abc_search_dropdown_item_icons_2line 0x7f0a0018
int layout abc_search_view 0x7f0a0019
int layout abc_select_dialog_material 0x7f0a001a
int layout abc_tooltip 0x7f0a001b
int layout activity_camera 0x7f0a001c
int layout activity_gallery 0x7f0a001d
int layout activity_main 0x7f0a001e
int layout custom_dialog 0x7f0a001f
int layout item_filter_category 0x7f0a0020
int layout item_filter_thumbnail 0x7f0a0021
int layout notification_action 0x7f0a0022
int layout notification_action_tombstone 0x7f0a0023
int layout notification_template_custom_big 0x7f0a0024
int layout notification_template_icon_group 0x7f0a0025
int layout notification_template_part_chronometer 0x7f0a0026
int layout notification_template_part_time 0x7f0a0027
int layout select_dialog_item_material 0x7f0a0028
int layout select_dialog_multichoice_material 0x7f0a0029
int layout select_dialog_singlechoice_material 0x7f0a002a
int layout support_simple_spinner_dropdown_item 0x7f0a002b
int menu example_menu 0x7f0b0000
int menu example_menu2 0x7f0b0001
int raw tone_cuver_sample 0x7f0c0000
int string abc_action_bar_home_description 0x7f0d0000
int string abc_action_bar_up_description 0x7f0d0001
int string abc_action_menu_overflow_description 0x7f0d0002
int string abc_action_mode_done 0x7f0d0003
int string abc_activity_chooser_view_see_all 0x7f0d0004
int string abc_activitychooserview_choose_application 0x7f0d0005
int string abc_capital_off 0x7f0d0006
int string abc_capital_on 0x7f0d0007
int string abc_menu_alt_shortcut_label 0x7f0d0008
int string abc_menu_ctrl_shortcut_label 0x7f0d0009
int string abc_menu_delete_shortcut_label 0x7f0d000a
int string abc_menu_enter_shortcut_label 0x7f0d000b
int string abc_menu_function_shortcut_label 0x7f0d000c
int string abc_menu_meta_shortcut_label 0x7f0d000d
int string abc_menu_shift_shortcut_label 0x7f0d000e
int string abc_menu_space_shortcut_label 0x7f0d000f
int string abc_menu_sym_shortcut_label 0x7f0d0010
int string abc_prepend_shortcut_label 0x7f0d0011
int string abc_search_hint 0x7f0d0012
int string abc_searchview_description_clear 0x7f0d0013
int string abc_searchview_description_query 0x7f0d0014
int string abc_searchview_description_search 0x7f0d0015
int string abc_searchview_description_submit 0x7f0d0016
int string abc_searchview_description_voice 0x7f0d0017
int string abc_shareactionprovider_share_with 0x7f0d0018
int string abc_shareactionprovider_share_with_application 0x7f0d0019
int string abc_toolbar_collapse_description 0x7f0d001a
int string app_name 0x7f0d001b
int string search_menu_title 0x7f0d001c
int string status_bar_notification_info_overflow 0x7f0d001d
int string title_activity_activity_main 0x7f0d001e
int style AlertDialog_AppCompat 0x7f0e0000
int style AlertDialog_AppCompat_Light 0x7f0e0001
int style Animation_AppCompat_Dialog 0x7f0e0002
int style Animation_AppCompat_DropDownUp 0x7f0e0003
int style Animation_AppCompat_Tooltip 0x7f0e0004
int style AppTheme 0x7f0e0005
int style AppTheme_NoActionBar 0x7f0e0006
int style Base_AlertDialog_AppCompat 0x7f0e0007
int style Base_AlertDialog_AppCompat_Light 0x7f0e0008
int style Base_Animation_AppCompat_Dialog 0x7f0e0009
int style Base_Animation_AppCompat_DropDownUp 0x7f0e000a
int style Base_Animation_AppCompat_Tooltip 0x7f0e000b
int style Base_DialogWindowTitle_AppCompat 0x7f0e000c
int style Base_DialogWindowTitleBackground_AppCompat 0x7f0e000d
int style Base_TextAppearance_AppCompat 0x7f0e000e
int style Base_TextAppearance_AppCompat_Body1 0x7f0e000f
int style Base_TextAppearance_AppCompat_Body2 0x7f0e0010
int style Base_TextAppearance_AppCompat_Button 0x7f0e0011
int style Base_TextAppearance_AppCompat_Caption 0x7f0e0012
int style Base_TextAppearance_AppCompat_Display1 0x7f0e0013
int style Base_TextAppearance_AppCompat_Display2 0x7f0e0014
int style Base_TextAppearance_AppCompat_Display3 0x7f0e0015
int style Base_TextAppearance_AppCompat_Display4 0x7f0e0016
int style Base_TextAppearance_AppCompat_Headline 0x7f0e0017
int style Base_TextAppearance_AppCompat_Inverse 0x7f0e0018
int style Base_TextAppearance_AppCompat_Large 0x7f0e0019
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f0e001a
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0e001b
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0e001c
int style Base_TextAppearance_AppCompat_Medium 0x7f0e001d
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f0e001e
int style Base_TextAppearance_AppCompat_Menu 0x7f0e001f
int style Base_TextAppearance_AppCompat_SearchResult 0x7f0e0020
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0e0021
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f0e0022
int style Base_TextAppearance_AppCompat_Small 0x7f0e0023
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f0e0024
int style Base_TextAppearance_AppCompat_Subhead 0x7f0e0025
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f0e0026
int style Base_TextAppearance_AppCompat_Title 0x7f0e0027
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f0e0028
int style Base_TextAppearance_AppCompat_Tooltip 0x7f0e0029
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0e002a
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0e002b
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0e002c
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0e002d
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0e002e
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0e002f
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0e0030
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f0e0031
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0e0032
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f0e0033
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0e0034
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f0e0035
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0e0036
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0e0037
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0e0038
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f0e0039
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0e003a
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0e003b
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0e003c
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0e003d
int style Base_Theme_AppCompat 0x7f0e003e
int style Base_Theme_AppCompat_CompactMenu 0x7f0e003f
int style Base_Theme_AppCompat_Dialog 0x7f0e0040
int style Base_Theme_AppCompat_Dialog_Alert 0x7f0e0041
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f0e0042
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f0e0043
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f0e0044
int style Base_Theme_AppCompat_Light 0x7f0e0045
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f0e0046
int style Base_Theme_AppCompat_Light_Dialog 0x7f0e0047
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f0e0048
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f0e0049
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f0e004a
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f0e004b
int style Base_ThemeOverlay_AppCompat 0x7f0e004c
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f0e004d
int style Base_ThemeOverlay_AppCompat_Dark 0x7f0e004e
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0e004f
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f0e0050
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f0e0051
int style Base_ThemeOverlay_AppCompat_Light 0x7f0e0052
int style Base_V21_Theme_AppCompat 0x7f0e0053
int style Base_V21_Theme_AppCompat_Dialog 0x7f0e0054
int style Base_V21_Theme_AppCompat_Light 0x7f0e0055
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f0e0056
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f0e0057
int style Base_V22_Theme_AppCompat 0x7f0e0058
int style Base_V22_Theme_AppCompat_Light 0x7f0e0059
int style Base_V23_Theme_AppCompat 0x7f0e005a
int style Base_V23_Theme_AppCompat_Light 0x7f0e005b
int style Base_V26_Theme_AppCompat 0x7f0e005c
int style Base_V26_Theme_AppCompat_Light 0x7f0e005d
int style Base_V26_Widget_AppCompat_Toolbar 0x7f0e005e
int style Base_V28_Theme_AppCompat 0x7f0e005f
int style Base_V28_Theme_AppCompat_Light 0x7f0e0060
int style Base_V7_Theme_AppCompat 0x7f0e0061
int style Base_V7_Theme_AppCompat_Dialog 0x7f0e0062
int style Base_V7_Theme_AppCompat_Light 0x7f0e0063
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f0e0064
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f0e0065
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f0e0066
int style Base_V7_Widget_AppCompat_EditText 0x7f0e0067
int style Base_V7_Widget_AppCompat_Toolbar 0x7f0e0068
int style Base_Widget_AppCompat_ActionBar 0x7f0e0069
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f0e006a
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f0e006b
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f0e006c
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f0e006d
int style Base_Widget_AppCompat_ActionButton 0x7f0e006e
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f0e006f
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f0e0070
int style Base_Widget_AppCompat_ActionMode 0x7f0e0071
int style Base_Widget_AppCompat_ActivityChooserView 0x7f0e0072
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f0e0073
int style Base_Widget_AppCompat_Button 0x7f0e0074
int style Base_Widget_AppCompat_Button_Borderless 0x7f0e0075
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f0e0076
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0e0077
int style Base_Widget_AppCompat_Button_Colored 0x7f0e0078
int style Base_Widget_AppCompat_Button_Small 0x7f0e0079
int style Base_Widget_AppCompat_ButtonBar 0x7f0e007a
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f0e007b
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f0e007c
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f0e007d
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f0e007e
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f0e007f
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f0e0080
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f0e0081
int style Base_Widget_AppCompat_EditText 0x7f0e0082
int style Base_Widget_AppCompat_ImageButton 0x7f0e0083
int style Base_Widget_AppCompat_Light_ActionBar 0x7f0e0084
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f0e0085
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f0e0086
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f0e0087
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0e0088
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f0e0089
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f0e008a
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0e008b
int style Base_Widget_AppCompat_ListMenuView 0x7f0e008c
int style Base_Widget_AppCompat_ListPopupWindow 0x7f0e008d
int style Base_Widget_AppCompat_ListView 0x7f0e008e
int style Base_Widget_AppCompat_ListView_DropDown 0x7f0e008f
int style Base_Widget_AppCompat_ListView_Menu 0x7f0e0090
int style Base_Widget_AppCompat_PopupMenu 0x7f0e0091
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f0e0092
int style Base_Widget_AppCompat_PopupWindow 0x7f0e0093
int style Base_Widget_AppCompat_ProgressBar 0x7f0e0094
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f0e0095
int style Base_Widget_AppCompat_RatingBar 0x7f0e0096
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f0e0097
int style Base_Widget_AppCompat_RatingBar_Small 0x7f0e0098
int style Base_Widget_AppCompat_SearchView 0x7f0e0099
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f0e009a
int style Base_Widget_AppCompat_SeekBar 0x7f0e009b
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f0e009c
int style Base_Widget_AppCompat_Spinner 0x7f0e009d
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f0e009e
int style Base_Widget_AppCompat_TextView 0x7f0e009f
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f0e00a0
int style Base_Widget_AppCompat_Toolbar 0x7f0e00a1
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f0e00a2
int style Platform_AppCompat 0x7f0e00a3
int style Platform_AppCompat_Light 0x7f0e00a4
int style Platform_ThemeOverlay_AppCompat 0x7f0e00a5
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f0e00a6
int style Platform_ThemeOverlay_AppCompat_Light 0x7f0e00a7
int style Platform_V21_AppCompat 0x7f0e00a8
int style Platform_V21_AppCompat_Light 0x7f0e00a9
int style Platform_V25_AppCompat 0x7f0e00aa
int style Platform_V25_AppCompat_Light 0x7f0e00ab
int style Platform_Widget_AppCompat_Spinner 0x7f0e00ac
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f0e00ad
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f0e00ae
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f0e00af
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f0e00b0
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f0e00b1
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f0e00b2
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f0e00b3
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f0e00b4
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f0e00b5
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f0e00b6
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f0e00b7
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f0e00b8
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f0e00b9
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f0e00ba
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f0e00bb
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f0e00bc
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f0e00bd
int style TextAppearance_AppCompat 0x7f0e00be
int style TextAppearance_AppCompat_Body1 0x7f0e00bf
int style TextAppearance_AppCompat_Body2 0x7f0e00c0
int style TextAppearance_AppCompat_Button 0x7f0e00c1
int style TextAppearance_AppCompat_Caption 0x7f0e00c2
int style TextAppearance_AppCompat_Display1 0x7f0e00c3
int style TextAppearance_AppCompat_Display2 0x7f0e00c4
int style TextAppearance_AppCompat_Display3 0x7f0e00c5
int style TextAppearance_AppCompat_Display4 0x7f0e00c6
int style TextAppearance_AppCompat_Headline 0x7f0e00c7
int style TextAppearance_AppCompat_Inverse 0x7f0e00c8
int style TextAppearance_AppCompat_Large 0x7f0e00c9
int style TextAppearance_AppCompat_Large_Inverse 0x7f0e00ca
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f0e00cb
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f0e00cc
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0e00cd
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0e00ce
int style TextAppearance_AppCompat_Medium 0x7f0e00cf
int style TextAppearance_AppCompat_Medium_Inverse 0x7f0e00d0
int style TextAppearance_AppCompat_Menu 0x7f0e00d1
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0e00d2
int style TextAppearance_AppCompat_SearchResult_Title 0x7f0e00d3
int style TextAppearance_AppCompat_Small 0x7f0e00d4
int style TextAppearance_AppCompat_Small_Inverse 0x7f0e00d5
int style TextAppearance_AppCompat_Subhead 0x7f0e00d6
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f0e00d7
int style TextAppearance_AppCompat_Title 0x7f0e00d8
int style TextAppearance_AppCompat_Title_Inverse 0x7f0e00d9
int style TextAppearance_AppCompat_Tooltip 0x7f0e00da
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0e00db
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0e00dc
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0e00dd
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0e00de
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0e00df
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0e00e0
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f0e00e1
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0e00e2
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f0e00e3
int style TextAppearance_AppCompat_Widget_Button 0x7f0e00e4
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0e00e5
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f0e00e6
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0e00e7
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f0e00e8
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0e00e9
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0e00ea
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0e00eb
int style TextAppearance_AppCompat_Widget_Switch 0x7f0e00ec
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0e00ed
int style TextAppearance_Compat_Notification 0x7f0e00ee
int style TextAppearance_Compat_Notification_Info 0x7f0e00ef
int style TextAppearance_Compat_Notification_Line2 0x7f0e00f0
int style TextAppearance_Compat_Notification_Time 0x7f0e00f1
int style TextAppearance_Compat_Notification_Title 0x7f0e00f2
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0e00f3
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0e00f4
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0e00f5
int style Theme_AppCompat 0x7f0e00f6
int style Theme_AppCompat_CompactMenu 0x7f0e00f7
int style Theme_AppCompat_DayNight 0x7f0e00f8
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f0e00f9
int style Theme_AppCompat_DayNight_Dialog 0x7f0e00fa
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f0e00fb
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f0e00fc
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f0e00fd
int style Theme_AppCompat_DayNight_NoActionBar 0x7f0e00fe
int style Theme_AppCompat_Dialog 0x7f0e00ff
int style Theme_AppCompat_Dialog_Alert 0x7f0e0100
int style Theme_AppCompat_Dialog_MinWidth 0x7f0e0101
int style Theme_AppCompat_DialogWhenLarge 0x7f0e0102
int style Theme_AppCompat_Empty 0x7f0e0103
int style Theme_AppCompat_Light 0x7f0e0104
int style Theme_AppCompat_Light_DarkActionBar 0x7f0e0105
int style Theme_AppCompat_Light_Dialog 0x7f0e0106
int style Theme_AppCompat_Light_Dialog_Alert 0x7f0e0107
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f0e0108
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f0e0109
int style Theme_AppCompat_Light_NoActionBar 0x7f0e010a
int style Theme_AppCompat_NoActionBar 0x7f0e010b
int style ThemeOverlay_AppCompat 0x7f0e010c
int style ThemeOverlay_AppCompat_ActionBar 0x7f0e010d
int style ThemeOverlay_AppCompat_Dark 0x7f0e010e
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0e010f
int style ThemeOverlay_AppCompat_DayNight 0x7f0e0110
int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x7f0e0111
int style ThemeOverlay_AppCompat_Dialog 0x7f0e0112
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f0e0113
int style ThemeOverlay_AppCompat_Light 0x7f0e0114
int style Widget_AppCompat_ActionBar 0x7f0e0115
int style Widget_AppCompat_ActionBar_Solid 0x7f0e0116
int style Widget_AppCompat_ActionBar_TabBar 0x7f0e0117
int style Widget_AppCompat_ActionBar_TabText 0x7f0e0118
int style Widget_AppCompat_ActionBar_TabView 0x7f0e0119
int style Widget_AppCompat_ActionButton 0x7f0e011a
int style Widget_AppCompat_ActionButton_CloseMode 0x7f0e011b
int style Widget_AppCompat_ActionButton_Overflow 0x7f0e011c
int style Widget_AppCompat_ActionMode 0x7f0e011d
int style Widget_AppCompat_ActivityChooserView 0x7f0e011e
int style Widget_AppCompat_AutoCompleteTextView 0x7f0e011f
int style Widget_AppCompat_Button 0x7f0e0120
int style Widget_AppCompat_Button_Borderless 0x7f0e0121
int style Widget_AppCompat_Button_Borderless_Colored 0x7f0e0122
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0e0123
int style Widget_AppCompat_Button_Colored 0x7f0e0124
int style Widget_AppCompat_Button_Small 0x7f0e0125
int style Widget_AppCompat_ButtonBar 0x7f0e0126
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f0e0127
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f0e0128
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f0e0129
int style Widget_AppCompat_CompoundButton_Switch 0x7f0e012a
int style Widget_AppCompat_DrawerArrowToggle 0x7f0e012b
int style Widget_AppCompat_DropDownItem_Spinner 0x7f0e012c
int style Widget_AppCompat_EditText 0x7f0e012d
int style Widget_AppCompat_ImageButton 0x7f0e012e
int style Widget_AppCompat_Light_ActionBar 0x7f0e012f
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f0e0130
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f0e0131
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f0e0132
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f0e0133
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f0e0134
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0e0135
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f0e0136
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f0e0137
int style Widget_AppCompat_Light_ActionButton 0x7f0e0138
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f0e0139
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f0e013a
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f0e013b
int style Widget_AppCompat_Light_ActivityChooserView 0x7f0e013c
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f0e013d
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f0e013e
int style Widget_AppCompat_Light_ListPopupWindow 0x7f0e013f
int style Widget_AppCompat_Light_ListView_DropDown 0x7f0e0140
int style Widget_AppCompat_Light_PopupMenu 0x7f0e0141
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0e0142
int style Widget_AppCompat_Light_SearchView 0x7f0e0143
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f0e0144
int style Widget_AppCompat_ListMenuView 0x7f0e0145
int style Widget_AppCompat_ListPopupWindow 0x7f0e0146
int style Widget_AppCompat_ListView 0x7f0e0147
int style Widget_AppCompat_ListView_DropDown 0x7f0e0148
int style Widget_AppCompat_ListView_Menu 0x7f0e0149
int style Widget_AppCompat_PopupMenu 0x7f0e014a
int style Widget_AppCompat_PopupMenu_Overflow 0x7f0e014b
int style Widget_AppCompat_PopupWindow 0x7f0e014c
int style Widget_AppCompat_ProgressBar 0x7f0e014d
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f0e014e
int style Widget_AppCompat_RatingBar 0x7f0e014f
int style Widget_AppCompat_RatingBar_Indicator 0x7f0e0150
int style Widget_AppCompat_RatingBar_Small 0x7f0e0151
int style Widget_AppCompat_SearchView 0x7f0e0152
int style Widget_AppCompat_SearchView_ActionBar 0x7f0e0153
int style Widget_AppCompat_SeekBar 0x7f0e0154
int style Widget_AppCompat_SeekBar_Discrete 0x7f0e0155
int style Widget_AppCompat_Spinner 0x7f0e0156
int style Widget_AppCompat_Spinner_DropDown 0x7f0e0157
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f0e0158
int style Widget_AppCompat_Spinner_Underlined 0x7f0e0159
int style Widget_AppCompat_TextView 0x7f0e015a
int style Widget_AppCompat_TextView_SpinnerItem 0x7f0e015b
int style Widget_AppCompat_Toolbar 0x7f0e015c
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f0e015d
int style Widget_Compat_NotificationActionContainer 0x7f0e015e
int style Widget_Compat_NotificationActionText 0x7f0e015f
int[] styleable ActionBar { 0x7f02003b, 0x7f02003c, 0x7f02003d, 0x7f020084, 0x7f020085, 0x7f020086, 0x7f020087, 0x7f020088, 0x7f020089, 0x7f020095, 0x7f0200a2, 0x7f0200a3, 0x7f0200bb, 0x7f0200e8, 0x7f0200e9, 0x7f0200ea, 0x7f0200eb, 0x7f0200ec, 0x7f0200f7, 0x7f0200fa, 0x7f02014d, 0x7f020173, 0x7f020192, 0x7f020195, 0x7f020196, 0x7f0201cf, 0x7f0201d2, 0x7f020200, 0x7f020209 }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f02003b, 0x7f02003c, 0x7f02006c, 0x7f0200e8, 0x7f0201d2, 0x7f020209 }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f0200bc, 0x7f0200f8 }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable AlertDialog { 0x010100f2, 0x7f020050, 0x7f020051, 0x7f020142, 0x7f020143, 0x7f020170, 0x7f0201bc, 0x7f0201bd }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppCompatImageView { 0x01010119, 0x7f0201c9, 0x7f0201fe, 0x7f0201ff }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f0201fb, 0x7f0201fc, 0x7f0201fd }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f020035, 0x7f020036, 0x7f020037, 0x7f020038, 0x7f020039, 0x7f0200ab, 0x7f0200ac, 0x7f0200ad, 0x7f0200ae, 0x7f0200b0, 0x7f0200b1, 0x7f0200b2, 0x7f0200b3, 0x7f0200c2, 0x7f0200d7, 0x7f0200e0, 0x7f0200fd, 0x7f02013d, 0x7f0201dc, 0x7f0201ed }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_firstBaselineToTopHeight 14
int styleable AppCompatTextView_fontFamily 15
int styleable AppCompatTextView_fontVariationSettings 16
int styleable AppCompatTextView_lastBaselineToBottomHeight 17
int styleable AppCompatTextView_lineHeight 18
int styleable AppCompatTextView_textAllCaps 19
int styleable AppCompatTextView_textLocale 20
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f020002, 0x7f020003, 0x7f020004, 0x7f020005, 0x7f020006, 0x7f020007, 0x7f020008, 0x7f020009, 0x7f02000a, 0x7f02000b, 0x7f02000c, 0x7f02000d, 0x7f02000e, 0x7f020010, 0x7f020011, 0x7f020012, 0x7f020013, 0x7f020014, 0x7f020015, 0x7f020016, 0x7f020017, 0x7f020018, 0x7f020019, 0x7f02001a, 0x7f02001b, 0x7f02001c, 0x7f02001d, 0x7f02001e, 0x7f02001f, 0x7f020020, 0x7f020023, 0x7f020024, 0x7f020025, 0x7f020026, 0x7f020027, 0x7f020034, 0x7f020047, 0x7f020049, 0x7f02004a, 0x7f02004b, 0x7f02004c, 0x7f02004d, 0x7f020052, 0x7f020053, 0x7f020061, 0x7f020062, 0x7f020070, 0x7f020071, 0x7f020072, 0x7f020073, 0x7f020074, 0x7f020075, 0x7f020076, 0x7f020077, 0x7f020078, 0x7f020079, 0x7f02008b, 0x7f02009f, 0x7f0200a0, 0x7f0200a1, 0x7f0200a4, 0x7f0200a6, 0x7f0200b5, 0x7f0200b6, 0x7f0200b8, 0x7f0200b9, 0x7f0200ba, 0x7f0200ea, 0x7f0200f2, 0x7f02013e, 0x7f02013f, 0x7f020140, 0x7f020141, 0x7f020144, 0x7f020145, 0x7f020146, 0x7f020147, 0x7f020148, 0x7f020149, 0x7f02014a, 0x7f02014b, 0x7f02014c, 0x7f020184, 0x7f020185, 0x7f020186, 0x7f020191, 0x7f020193, 0x7f02019d, 0x7f02019e, 0x7f02019f, 0x7f0201a0, 0x7f0201b2, 0x7f0201b3, 0x7f0201b4, 0x7f0201b5, 0x7f0201c1, 0x7f0201c2, 0x7f0201d6, 0x7f0201dd, 0x7f0201de, 0x7f0201df, 0x7f0201e0, 0x7f0201e1, 0x7f0201e2, 0x7f0201e3, 0x7f0201e4, 0x7f0201ea, 0x7f0201eb, 0x7f02020a, 0x7f02020b, 0x7f02020c, 0x7f02020d, 0x7f02021f, 0x7f02022d, 0x7f02022e, 0x7f02022f, 0x7f020230, 0x7f020231, 0x7f020232, 0x7f020233, 0x7f020234, 0x7f020235, 0x7f020236 }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseDrawable 19
int styleable AppCompatTheme_actionModeCopyDrawable 20
int styleable AppCompatTheme_actionModeCutDrawable 21
int styleable AppCompatTheme_actionModeFindDrawable 22
int styleable AppCompatTheme_actionModePasteDrawable 23
int styleable AppCompatTheme_actionModePopupWindowStyle 24
int styleable AppCompatTheme_actionModeSelectAllDrawable 25
int styleable AppCompatTheme_actionModeShareDrawable 26
int styleable AppCompatTheme_actionModeSplitBackground 27
int styleable AppCompatTheme_actionModeStyle 28
int styleable AppCompatTheme_actionModeWebSearchDrawable 29
int styleable AppCompatTheme_actionOverflowButtonStyle 30
int styleable AppCompatTheme_actionOverflowMenuStyle 31
int styleable AppCompatTheme_activityChooserViewStyle 32
int styleable AppCompatTheme_alertDialogButtonGroupStyle 33
int styleable AppCompatTheme_alertDialogCenterButtons 34
int styleable AppCompatTheme_alertDialogStyle 35
int styleable AppCompatTheme_alertDialogTheme 36
int styleable AppCompatTheme_autoCompleteTextViewStyle 37
int styleable AppCompatTheme_borderlessButtonStyle 38
int styleable AppCompatTheme_buttonBarButtonStyle 39
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 40
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 41
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 42
int styleable AppCompatTheme_buttonBarStyle 43
int styleable AppCompatTheme_buttonStyle 44
int styleable AppCompatTheme_buttonStyleSmall 45
int styleable AppCompatTheme_checkboxStyle 46
int styleable AppCompatTheme_checkedTextViewStyle 47
int styleable AppCompatTheme_colorAccent 48
int styleable AppCompatTheme_colorBackgroundFloating 49
int styleable AppCompatTheme_colorButtonNormal 50
int styleable AppCompatTheme_colorControlActivated 51
int styleable AppCompatTheme_colorControlHighlight 52
int styleable AppCompatTheme_colorControlNormal 53
int styleable AppCompatTheme_colorError 54
int styleable AppCompatTheme_colorPrimary 55
int styleable AppCompatTheme_colorPrimaryDark 56
int styleable AppCompatTheme_colorSwitchThumbNormal 57
int styleable AppCompatTheme_controlBackground 58
int styleable AppCompatTheme_dialogCornerRadius 59
int styleable AppCompatTheme_dialogPreferredPadding 60
int styleable AppCompatTheme_dialogTheme 61
int styleable AppCompatTheme_dividerHorizontal 62
int styleable AppCompatTheme_dividerVertical 63
int styleable AppCompatTheme_dropDownListViewStyle 64
int styleable AppCompatTheme_dropdownListPreferredItemHeight 65
int styleable AppCompatTheme_editTextBackground 66
int styleable AppCompatTheme_editTextColor 67
int styleable AppCompatTheme_editTextStyle 68
int styleable AppCompatTheme_homeAsUpIndicator 69
int styleable AppCompatTheme_imageButtonStyle 70
int styleable AppCompatTheme_listChoiceBackgroundIndicator 71
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 72
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 73
int styleable AppCompatTheme_listDividerAlertDialog 74
int styleable AppCompatTheme_listMenuViewStyle 75
int styleable AppCompatTheme_listPopupWindowStyle 76
int styleable AppCompatTheme_listPreferredItemHeight 77
int styleable AppCompatTheme_listPreferredItemHeightLarge 78
int styleable AppCompatTheme_listPreferredItemHeightSmall 79
int styleable AppCompatTheme_listPreferredItemPaddingEnd 80
int styleable AppCompatTheme_listPreferredItemPaddingLeft 81
int styleable AppCompatTheme_listPreferredItemPaddingRight 82
int styleable AppCompatTheme_listPreferredItemPaddingStart 83
int styleable AppCompatTheme_panelBackground 84
int styleable AppCompatTheme_panelMenuListTheme 85
int styleable AppCompatTheme_panelMenuListWidth 86
int styleable AppCompatTheme_popupMenuStyle 87
int styleable AppCompatTheme_popupWindowStyle 88
int styleable AppCompatTheme_radioButtonStyle 89
int styleable AppCompatTheme_ratingBarStyle 90
int styleable AppCompatTheme_ratingBarStyleIndicator 91
int styleable AppCompatTheme_ratingBarStyleSmall 92
int styleable AppCompatTheme_searchViewStyle 93
int styleable AppCompatTheme_seekBarStyle 94
int styleable AppCompatTheme_selectableItemBackground 95
int styleable AppCompatTheme_selectableItemBackgroundBorderless 96
int styleable AppCompatTheme_spinnerDropDownItemStyle 97
int styleable AppCompatTheme_spinnerStyle 98
int styleable AppCompatTheme_switchStyle 99
int styleable AppCompatTheme_textAppearanceLargePopupMenu 100
int styleable AppCompatTheme_textAppearanceListItem 101
int styleable AppCompatTheme_textAppearanceListItemSecondary 102
int styleable AppCompatTheme_textAppearanceListItemSmall 103
int styleable AppCompatTheme_textAppearancePopupMenuHeader 104
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 105
int styleable AppCompatTheme_textAppearanceSearchResultTitle 106
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 107
int styleable AppCompatTheme_textColorAlertDialogListItem 108
int styleable AppCompatTheme_textColorSearchUrl 109
int styleable AppCompatTheme_toolbarNavigationButtonStyle 110
int styleable AppCompatTheme_toolbarStyle 111
int styleable AppCompatTheme_tooltipForegroundColor 112
int styleable AppCompatTheme_tooltipFrameBackground 113
int styleable AppCompatTheme_viewInflaterClass 114
int styleable AppCompatTheme_windowActionBar 115
int styleable AppCompatTheme_windowActionBarOverlay 116
int styleable AppCompatTheme_windowActionModeOverlay 117
int styleable AppCompatTheme_windowFixedHeightMajor 118
int styleable AppCompatTheme_windowFixedHeightMinor 119
int styleable AppCompatTheme_windowFixedWidthMajor 120
int styleable AppCompatTheme_windowFixedWidthMinor 121
int styleable AppCompatTheme_windowMinWidthMajor 122
int styleable AppCompatTheme_windowMinWidthMinor 123
int styleable AppCompatTheme_windowNoTitle 124
int[] styleable ButtonBarLayout { 0x7f020028 }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable Capability { 0x7f02019c, 0x7f0201b7 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable Carousel { 0x7f020056, 0x7f020057, 0x7f020058, 0x7f020059, 0x7f02005a, 0x7f02005b, 0x7f02005c, 0x7f02005d, 0x7f02005e, 0x7f02005f }
int styleable Carousel_carousel_backwardTransition 0
int styleable Carousel_carousel_emptyViewsBehavior 1
int styleable Carousel_carousel_firstView 2
int styleable Carousel_carousel_forwardTransition 3
int styleable Carousel_carousel_infinite 4
int styleable Carousel_carousel_nextState 5
int styleable Carousel_carousel_previousState 6
int styleable Carousel_carousel_touchUpMode 7
int styleable Carousel_carousel_touchUp_dampeningFactor 8
int styleable Carousel_carousel_touchUp_velocityThreshold 9
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f020029, 0x7f0200fc }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x01010107, 0x7f02004e, 0x7f020054, 0x7f020055 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable Constraint { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f02002c, 0x7f02002d, 0x7f020041, 0x7f020042, 0x7f020043, 0x7f020060, 0x7f02007f, 0x7f020080, 0x7f0200aa, 0x7f0200c3, 0x7f0200c4, 0x7f0200c5, 0x7f0200c6, 0x7f0200c7, 0x7f0200c8, 0x7f0200c9, 0x7f0200ca, 0x7f0200cb, 0x7f0200cc, 0x7f0200cd, 0x7f0200ce, 0x7f0200cf, 0x7f0200d1, 0x7f0200d2, 0x7f0200d3, 0x7f0200d4, 0x7f0200d5, 0x7f0200e7, 0x7f020102, 0x7f020103, 0x7f020104, 0x7f020105, 0x7f020106, 0x7f020107, 0x7f020108, 0x7f020109, 0x7f02010a, 0x7f02010b, 0x7f02010c, 0x7f02010d, 0x7f02010e, 0x7f02010f, 0x7f020110, 0x7f020111, 0x7f020112, 0x7f020113, 0x7f020114, 0x7f020115, 0x7f020116, 0x7f020117, 0x7f020118, 0x7f020119, 0x7f02011a, 0x7f02011b, 0x7f02011c, 0x7f02011d, 0x7f02011e, 0x7f02011f, 0x7f020120, 0x7f020121, 0x7f020122, 0x7f020123, 0x7f020124, 0x7f020125, 0x7f020126, 0x7f020127, 0x7f020128, 0x7f020129, 0x7f02012a, 0x7f02012b, 0x7f02012c, 0x7f02012d, 0x7f02012e, 0x7f02012f, 0x7f020130, 0x7f020131, 0x7f020132, 0x7f020133, 0x7f020134, 0x7f020135, 0x7f020136, 0x7f020137, 0x7f020138, 0x7f020139, 0x7f02013b, 0x7f02016a, 0x7f02016b, 0x7f020187, 0x7f02018e, 0x7f020190, 0x7f020197, 0x7f020198, 0x7f020199, 0x7f020215, 0x7f020217, 0x7f020219, 0x7f020224 }
int styleable Constraint_android_orientation 0
int styleable Constraint_android_id 1
int styleable Constraint_android_visibility 2
int styleable Constraint_android_layout_width 3
int styleable Constraint_android_layout_height 4
int styleable Constraint_android_layout_marginLeft 5
int styleable Constraint_android_layout_marginTop 6
int styleable Constraint_android_layout_marginRight 7
int styleable Constraint_android_layout_marginBottom 8
int styleable Constraint_android_maxWidth 9
int styleable Constraint_android_maxHeight 10
int styleable Constraint_android_minWidth 11
int styleable Constraint_android_minHeight 12
int styleable Constraint_android_alpha 13
int styleable Constraint_android_transformPivotX 14
int styleable Constraint_android_transformPivotY 15
int styleable Constraint_android_translationX 16
int styleable Constraint_android_translationY 17
int styleable Constraint_android_scaleX 18
int styleable Constraint_android_scaleY 19
int styleable Constraint_android_rotation 20
int styleable Constraint_android_rotationX 21
int styleable Constraint_android_rotationY 22
int styleable Constraint_android_layout_marginStart 23
int styleable Constraint_android_layout_marginEnd 24
int styleable Constraint_android_translationZ 25
int styleable Constraint_android_elevation 26
int styleable Constraint_animateCircleAngleTo 27
int styleable Constraint_animateRelativeTo 28
int styleable Constraint_barrierAllowsGoneWidgets 29
int styleable Constraint_barrierDirection 30
int styleable Constraint_barrierMargin 31
int styleable Constraint_chainUseRtl 32
int styleable Constraint_constraint_referenced_ids 33
int styleable Constraint_constraint_referenced_tags 34
int styleable Constraint_drawPath 35
int styleable Constraint_flow_firstHorizontalBias 36
int styleable Constraint_flow_firstHorizontalStyle 37
int styleable Constraint_flow_firstVerticalBias 38
int styleable Constraint_flow_firstVerticalStyle 39
int styleable Constraint_flow_horizontalAlign 40
int styleable Constraint_flow_horizontalBias 41
int styleable Constraint_flow_horizontalGap 42
int styleable Constraint_flow_horizontalStyle 43
int styleable Constraint_flow_lastHorizontalBias 44
int styleable Constraint_flow_lastHorizontalStyle 45
int styleable Constraint_flow_lastVerticalBias 46
int styleable Constraint_flow_lastVerticalStyle 47
int styleable Constraint_flow_maxElementsWrap 48
int styleable Constraint_flow_verticalAlign 49
int styleable Constraint_flow_verticalBias 50
int styleable Constraint_flow_verticalGap 51
int styleable Constraint_flow_verticalStyle 52
int styleable Constraint_flow_wrapMode 53
int styleable Constraint_guidelineUseRtl 54
int styleable Constraint_layout_constrainedHeight 55
int styleable Constraint_layout_constrainedWidth 56
int styleable Constraint_layout_constraintBaseline_creator 57
int styleable Constraint_layout_constraintBaseline_toBaselineOf 58
int styleable Constraint_layout_constraintBaseline_toBottomOf 59
int styleable Constraint_layout_constraintBaseline_toTopOf 60
int styleable Constraint_layout_constraintBottom_creator 61
int styleable Constraint_layout_constraintBottom_toBottomOf 62
int styleable Constraint_layout_constraintBottom_toTopOf 63
int styleable Constraint_layout_constraintCircle 64
int styleable Constraint_layout_constraintCircleAngle 65
int styleable Constraint_layout_constraintCircleRadius 66
int styleable Constraint_layout_constraintDimensionRatio 67
int styleable Constraint_layout_constraintEnd_toEndOf 68
int styleable Constraint_layout_constraintEnd_toStartOf 69
int styleable Constraint_layout_constraintGuide_begin 70
int styleable Constraint_layout_constraintGuide_end 71
int styleable Constraint_layout_constraintGuide_percent 72
int styleable Constraint_layout_constraintHeight 73
int styleable Constraint_layout_constraintHeight_default 74
int styleable Constraint_layout_constraintHeight_max 75
int styleable Constraint_layout_constraintHeight_min 76
int styleable Constraint_layout_constraintHeight_percent 77
int styleable Constraint_layout_constraintHorizontal_bias 78
int styleable Constraint_layout_constraintHorizontal_chainStyle 79
int styleable Constraint_layout_constraintHorizontal_weight 80
int styleable Constraint_layout_constraintLeft_creator 81
int styleable Constraint_layout_constraintLeft_toLeftOf 82
int styleable Constraint_layout_constraintLeft_toRightOf 83
int styleable Constraint_layout_constraintRight_creator 84
int styleable Constraint_layout_constraintRight_toLeftOf 85
int styleable Constraint_layout_constraintRight_toRightOf 86
int styleable Constraint_layout_constraintStart_toEndOf 87
int styleable Constraint_layout_constraintStart_toStartOf 88
int styleable Constraint_layout_constraintTag 89
int styleable Constraint_layout_constraintTop_creator 90
int styleable Constraint_layout_constraintTop_toBottomOf 91
int styleable Constraint_layout_constraintTop_toTopOf 92
int styleable Constraint_layout_constraintVertical_bias 93
int styleable Constraint_layout_constraintVertical_chainStyle 94
int styleable Constraint_layout_constraintVertical_weight 95
int styleable Constraint_layout_constraintWidth 96
int styleable Constraint_layout_constraintWidth_default 97
int styleable Constraint_layout_constraintWidth_max 98
int styleable Constraint_layout_constraintWidth_min 99
int styleable Constraint_layout_constraintWidth_percent 100
int styleable Constraint_layout_editor_absoluteX 101
int styleable Constraint_layout_editor_absoluteY 102
int styleable Constraint_layout_goneMarginBaseline 103
int styleable Constraint_layout_goneMarginBottom 104
int styleable Constraint_layout_goneMarginEnd 105
int styleable Constraint_layout_goneMarginLeft 106
int styleable Constraint_layout_goneMarginRight 107
int styleable Constraint_layout_goneMarginStart 108
int styleable Constraint_layout_goneMarginTop 109
int styleable Constraint_layout_marginBaseline 110
int styleable Constraint_layout_wrapBehaviorInParent 111
int styleable Constraint_motionProgress 112
int styleable Constraint_motionStagger 113
int styleable Constraint_pathMotionArc 114
int styleable Constraint_pivotAnchor 115
int styleable Constraint_polarRelativeTo 116
int styleable Constraint_quantizeMotionInterpolator 117
int styleable Constraint_quantizeMotionPhase 118
int styleable Constraint_quantizeMotionSteps 119
int styleable Constraint_transformPivotTarget 120
int styleable Constraint_transitionEasing 121
int styleable Constraint_transitionPathRotate 122
int styleable Constraint_visibilityMode 123
int[] styleable ConstraintLayout_Layout { 0x010100c4, 0x010100d5, 0x010100d6, 0x010100d7, 0x010100d8, 0x010100d9, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f6, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010103b3, 0x010103b4, 0x010103b5, 0x010103b6, 0x01010440, 0x0101053b, 0x0101053c, 0x7f020041, 0x7f020042, 0x7f020043, 0x7f020060, 0x7f020064, 0x7f020065, 0x7f020066, 0x7f020067, 0x7f020068, 0x7f02007c, 0x7f02007f, 0x7f020080, 0x7f0200c3, 0x7f0200c4, 0x7f0200c5, 0x7f0200c6, 0x7f0200c7, 0x7f0200c8, 0x7f0200c9, 0x7f0200ca, 0x7f0200cb, 0x7f0200cc, 0x7f0200cd, 0x7f0200ce, 0x7f0200cf, 0x7f0200d1, 0x7f0200d2, 0x7f0200d3, 0x7f0200d4, 0x7f0200d5, 0x7f0200e7, 0x7f0200ff, 0x7f020102, 0x7f020103, 0x7f020104, 0x7f020105, 0x7f020106, 0x7f020107, 0x7f020108, 0x7f020109, 0x7f02010a, 0x7f02010b, 0x7f02010c, 0x7f02010d, 0x7f02010e, 0x7f02010f, 0x7f020110, 0x7f020111, 0x7f020112, 0x7f020113, 0x7f020114, 0x7f020115, 0x7f020116, 0x7f020117, 0x7f020118, 0x7f020119, 0x7f02011a, 0x7f02011b, 0x7f02011c, 0x7f02011d, 0x7f02011e, 0x7f02011f, 0x7f020120, 0x7f020121, 0x7f020122, 0x7f020123, 0x7f020124, 0x7f020125, 0x7f020126, 0x7f020127, 0x7f020128, 0x7f020129, 0x7f02012a, 0x7f02012b, 0x7f02012c, 0x7f02012d, 0x7f02012e, 0x7f02012f, 0x7f020130, 0x7f020131, 0x7f020132, 0x7f020133, 0x7f020134, 0x7f020135, 0x7f020136, 0x7f020137, 0x7f020138, 0x7f020139, 0x7f02013a, 0x7f02013b }
int styleable ConstraintLayout_Layout_android_orientation 0
int styleable ConstraintLayout_Layout_android_padding 1
int styleable ConstraintLayout_Layout_android_paddingLeft 2
int styleable ConstraintLayout_Layout_android_paddingTop 3
int styleable ConstraintLayout_Layout_android_paddingRight 4
int styleable ConstraintLayout_Layout_android_paddingBottom 5
int styleable ConstraintLayout_Layout_android_visibility 6
int styleable ConstraintLayout_Layout_android_layout_width 7
int styleable ConstraintLayout_Layout_android_layout_height 8
int styleable ConstraintLayout_Layout_android_layout_margin 9
int styleable ConstraintLayout_Layout_android_layout_marginLeft 10
int styleable ConstraintLayout_Layout_android_layout_marginTop 11
int styleable ConstraintLayout_Layout_android_layout_marginRight 12
int styleable ConstraintLayout_Layout_android_layout_marginBottom 13
int styleable ConstraintLayout_Layout_android_maxWidth 14
int styleable ConstraintLayout_Layout_android_maxHeight 15
int styleable ConstraintLayout_Layout_android_minWidth 16
int styleable ConstraintLayout_Layout_android_minHeight 17
int styleable ConstraintLayout_Layout_android_paddingStart 18
int styleable ConstraintLayout_Layout_android_paddingEnd 19
int styleable ConstraintLayout_Layout_android_layout_marginStart 20
int styleable ConstraintLayout_Layout_android_layout_marginEnd 21
int styleable ConstraintLayout_Layout_android_elevation 22
int styleable ConstraintLayout_Layout_android_layout_marginHorizontal 23
int styleable ConstraintLayout_Layout_android_layout_marginVertical 24
int styleable ConstraintLayout_Layout_barrierAllowsGoneWidgets 25
int styleable ConstraintLayout_Layout_barrierDirection 26
int styleable ConstraintLayout_Layout_barrierMargin 27
int styleable ConstraintLayout_Layout_chainUseRtl 28
int styleable ConstraintLayout_Layout_circularflow_angles 29
int styleable ConstraintLayout_Layout_circularflow_defaultAngle 30
int styleable ConstraintLayout_Layout_circularflow_defaultRadius 31
int styleable ConstraintLayout_Layout_circularflow_radiusInDP 32
int styleable ConstraintLayout_Layout_circularflow_viewCenter 33
int styleable ConstraintLayout_Layout_constraintSet 34
int styleable ConstraintLayout_Layout_constraint_referenced_ids 35
int styleable ConstraintLayout_Layout_constraint_referenced_tags 36
int styleable ConstraintLayout_Layout_flow_firstHorizontalBias 37
int styleable ConstraintLayout_Layout_flow_firstHorizontalStyle 38
int styleable ConstraintLayout_Layout_flow_firstVerticalBias 39
int styleable ConstraintLayout_Layout_flow_firstVerticalStyle 40
int styleable ConstraintLayout_Layout_flow_horizontalAlign 41
int styleable ConstraintLayout_Layout_flow_horizontalBias 42
int styleable ConstraintLayout_Layout_flow_horizontalGap 43
int styleable ConstraintLayout_Layout_flow_horizontalStyle 44
int styleable ConstraintLayout_Layout_flow_lastHorizontalBias 45
int styleable ConstraintLayout_Layout_flow_lastHorizontalStyle 46
int styleable ConstraintLayout_Layout_flow_lastVerticalBias 47
int styleable ConstraintLayout_Layout_flow_lastVerticalStyle 48
int styleable ConstraintLayout_Layout_flow_maxElementsWrap 49
int styleable ConstraintLayout_Layout_flow_verticalAlign 50
int styleable ConstraintLayout_Layout_flow_verticalBias 51
int styleable ConstraintLayout_Layout_flow_verticalGap 52
int styleable ConstraintLayout_Layout_flow_verticalStyle 53
int styleable ConstraintLayout_Layout_flow_wrapMode 54
int styleable ConstraintLayout_Layout_guidelineUseRtl 55
int styleable ConstraintLayout_Layout_layoutDescription 56
int styleable ConstraintLayout_Layout_layout_constrainedHeight 57
int styleable ConstraintLayout_Layout_layout_constrainedWidth 58
int styleable ConstraintLayout_Layout_layout_constraintBaseline_creator 59
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf 60
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBottomOf 61
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toTopOf 62
int styleable ConstraintLayout_Layout_layout_constraintBottom_creator 63
int styleable ConstraintLayout_Layout_layout_constraintBottom_toBottomOf 64
int styleable ConstraintLayout_Layout_layout_constraintBottom_toTopOf 65
int styleable ConstraintLayout_Layout_layout_constraintCircle 66
int styleable ConstraintLayout_Layout_layout_constraintCircleAngle 67
int styleable ConstraintLayout_Layout_layout_constraintCircleRadius 68
int styleable ConstraintLayout_Layout_layout_constraintDimensionRatio 69
int styleable ConstraintLayout_Layout_layout_constraintEnd_toEndOf 70
int styleable ConstraintLayout_Layout_layout_constraintEnd_toStartOf 71
int styleable ConstraintLayout_Layout_layout_constraintGuide_begin 72
int styleable ConstraintLayout_Layout_layout_constraintGuide_end 73
int styleable ConstraintLayout_Layout_layout_constraintGuide_percent 74
int styleable ConstraintLayout_Layout_layout_constraintHeight 75
int styleable ConstraintLayout_Layout_layout_constraintHeight_default 76
int styleable ConstraintLayout_Layout_layout_constraintHeight_max 77
int styleable ConstraintLayout_Layout_layout_constraintHeight_min 78
int styleable ConstraintLayout_Layout_layout_constraintHeight_percent 79
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_bias 80
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle 81
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_weight 82
int styleable ConstraintLayout_Layout_layout_constraintLeft_creator 83
int styleable ConstraintLayout_Layout_layout_constraintLeft_toLeftOf 84
int styleable ConstraintLayout_Layout_layout_constraintLeft_toRightOf 85
int styleable ConstraintLayout_Layout_layout_constraintRight_creator 86
int styleable ConstraintLayout_Layout_layout_constraintRight_toLeftOf 87
int styleable ConstraintLayout_Layout_layout_constraintRight_toRightOf 88
int styleable ConstraintLayout_Layout_layout_constraintStart_toEndOf 89
int styleable ConstraintLayout_Layout_layout_constraintStart_toStartOf 90
int styleable ConstraintLayout_Layout_layout_constraintTag 91
int styleable ConstraintLayout_Layout_layout_constraintTop_creator 92
int styleable ConstraintLayout_Layout_layout_constraintTop_toBottomOf 93
int styleable ConstraintLayout_Layout_layout_constraintTop_toTopOf 94
int styleable ConstraintLayout_Layout_layout_constraintVertical_bias 95
int styleable ConstraintLayout_Layout_layout_constraintVertical_chainStyle 96
int styleable ConstraintLayout_Layout_layout_constraintVertical_weight 97
int styleable ConstraintLayout_Layout_layout_constraintWidth 98
int styleable ConstraintLayout_Layout_layout_constraintWidth_default 99
int styleable ConstraintLayout_Layout_layout_constraintWidth_max 100
int styleable ConstraintLayout_Layout_layout_constraintWidth_min 101
int styleable ConstraintLayout_Layout_layout_constraintWidth_percent 102
int styleable ConstraintLayout_Layout_layout_editor_absoluteX 103
int styleable ConstraintLayout_Layout_layout_editor_absoluteY 104
int styleable ConstraintLayout_Layout_layout_goneMarginBaseline 105
int styleable ConstraintLayout_Layout_layout_goneMarginBottom 106
int styleable ConstraintLayout_Layout_layout_goneMarginEnd 107
int styleable ConstraintLayout_Layout_layout_goneMarginLeft 108
int styleable ConstraintLayout_Layout_layout_goneMarginRight 109
int styleable ConstraintLayout_Layout_layout_goneMarginStart 110
int styleable ConstraintLayout_Layout_layout_goneMarginTop 111
int styleable ConstraintLayout_Layout_layout_marginBaseline 112
int styleable ConstraintLayout_Layout_layout_optimizationLevel 113
int styleable ConstraintLayout_Layout_layout_wrapBehaviorInParent 114
int[] styleable ConstraintLayout_ReactiveGuide { 0x7f0201a1, 0x7f0201a2, 0x7f0201a3, 0x7f0201a4 }
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_animateChange 0
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_applyToAllConstraintSets 1
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_applyToConstraintSet 2
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_valueId 3
int[] styleable ConstraintLayout_placeholder { 0x7f020082, 0x7f02018f }
int styleable ConstraintLayout_placeholder_content 0
int styleable ConstraintLayout_placeholder_placeholder_emptyVisibility 1
int[] styleable ConstraintOverride { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f02002c, 0x7f02002d, 0x7f020041, 0x7f020042, 0x7f020043, 0x7f020060, 0x7f02007f, 0x7f0200aa, 0x7f0200c3, 0x7f0200c4, 0x7f0200c5, 0x7f0200c6, 0x7f0200c7, 0x7f0200c8, 0x7f0200c9, 0x7f0200ca, 0x7f0200cb, 0x7f0200cc, 0x7f0200cd, 0x7f0200ce, 0x7f0200cf, 0x7f0200d1, 0x7f0200d2, 0x7f0200d3, 0x7f0200d4, 0x7f0200d5, 0x7f0200e7, 0x7f020102, 0x7f020103, 0x7f020104, 0x7f020108, 0x7f02010c, 0x7f02010d, 0x7f02010e, 0x7f020111, 0x7f020112, 0x7f020113, 0x7f020114, 0x7f020115, 0x7f020116, 0x7f020117, 0x7f020118, 0x7f020119, 0x7f02011a, 0x7f02011b, 0x7f02011c, 0x7f02011f, 0x7f020124, 0x7f020125, 0x7f020128, 0x7f020129, 0x7f02012a, 0x7f02012b, 0x7f02012c, 0x7f02012d, 0x7f02012e, 0x7f02012f, 0x7f020130, 0x7f020131, 0x7f020132, 0x7f020133, 0x7f020134, 0x7f020135, 0x7f020136, 0x7f020137, 0x7f020138, 0x7f020139, 0x7f02013b, 0x7f02016a, 0x7f02016b, 0x7f02016c, 0x7f020187, 0x7f02018e, 0x7f020190, 0x7f020197, 0x7f020198, 0x7f020199, 0x7f020215, 0x7f020217, 0x7f020219, 0x7f020224 }
int styleable ConstraintOverride_android_orientation 0
int styleable ConstraintOverride_android_id 1
int styleable ConstraintOverride_android_visibility 2
int styleable ConstraintOverride_android_layout_width 3
int styleable ConstraintOverride_android_layout_height 4
int styleable ConstraintOverride_android_layout_marginLeft 5
int styleable ConstraintOverride_android_layout_marginTop 6
int styleable ConstraintOverride_android_layout_marginRight 7
int styleable ConstraintOverride_android_layout_marginBottom 8
int styleable ConstraintOverride_android_maxWidth 9
int styleable ConstraintOverride_android_maxHeight 10
int styleable ConstraintOverride_android_minWidth 11
int styleable ConstraintOverride_android_minHeight 12
int styleable ConstraintOverride_android_alpha 13
int styleable ConstraintOverride_android_transformPivotX 14
int styleable ConstraintOverride_android_transformPivotY 15
int styleable ConstraintOverride_android_translationX 16
int styleable ConstraintOverride_android_translationY 17
int styleable ConstraintOverride_android_scaleX 18
int styleable ConstraintOverride_android_scaleY 19
int styleable ConstraintOverride_android_rotation 20
int styleable ConstraintOverride_android_rotationX 21
int styleable ConstraintOverride_android_rotationY 22
int styleable ConstraintOverride_android_layout_marginStart 23
int styleable ConstraintOverride_android_layout_marginEnd 24
int styleable ConstraintOverride_android_translationZ 25
int styleable ConstraintOverride_android_elevation 26
int styleable ConstraintOverride_animateCircleAngleTo 27
int styleable ConstraintOverride_animateRelativeTo 28
int styleable ConstraintOverride_barrierAllowsGoneWidgets 29
int styleable ConstraintOverride_barrierDirection 30
int styleable ConstraintOverride_barrierMargin 31
int styleable ConstraintOverride_chainUseRtl 32
int styleable ConstraintOverride_constraint_referenced_ids 33
int styleable ConstraintOverride_drawPath 34
int styleable ConstraintOverride_flow_firstHorizontalBias 35
int styleable ConstraintOverride_flow_firstHorizontalStyle 36
int styleable ConstraintOverride_flow_firstVerticalBias 37
int styleable ConstraintOverride_flow_firstVerticalStyle 38
int styleable ConstraintOverride_flow_horizontalAlign 39
int styleable ConstraintOverride_flow_horizontalBias 40
int styleable ConstraintOverride_flow_horizontalGap 41
int styleable ConstraintOverride_flow_horizontalStyle 42
int styleable ConstraintOverride_flow_lastHorizontalBias 43
int styleable ConstraintOverride_flow_lastHorizontalStyle 44
int styleable ConstraintOverride_flow_lastVerticalBias 45
int styleable ConstraintOverride_flow_lastVerticalStyle 46
int styleable ConstraintOverride_flow_maxElementsWrap 47
int styleable ConstraintOverride_flow_verticalAlign 48
int styleable ConstraintOverride_flow_verticalBias 49
int styleable ConstraintOverride_flow_verticalGap 50
int styleable ConstraintOverride_flow_verticalStyle 51
int styleable ConstraintOverride_flow_wrapMode 52
int styleable ConstraintOverride_guidelineUseRtl 53
int styleable ConstraintOverride_layout_constrainedHeight 54
int styleable ConstraintOverride_layout_constrainedWidth 55
int styleable ConstraintOverride_layout_constraintBaseline_creator 56
int styleable ConstraintOverride_layout_constraintBottom_creator 57
int styleable ConstraintOverride_layout_constraintCircleAngle 58
int styleable ConstraintOverride_layout_constraintCircleRadius 59
int styleable ConstraintOverride_layout_constraintDimensionRatio 60
int styleable ConstraintOverride_layout_constraintGuide_begin 61
int styleable ConstraintOverride_layout_constraintGuide_end 62
int styleable ConstraintOverride_layout_constraintGuide_percent 63
int styleable ConstraintOverride_layout_constraintHeight 64
int styleable ConstraintOverride_layout_constraintHeight_default 65
int styleable ConstraintOverride_layout_constraintHeight_max 66
int styleable ConstraintOverride_layout_constraintHeight_min 67
int styleable ConstraintOverride_layout_constraintHeight_percent 68
int styleable ConstraintOverride_layout_constraintHorizontal_bias 69
int styleable ConstraintOverride_layout_constraintHorizontal_chainStyle 70
int styleable ConstraintOverride_layout_constraintHorizontal_weight 71
int styleable ConstraintOverride_layout_constraintLeft_creator 72
int styleable ConstraintOverride_layout_constraintRight_creator 73
int styleable ConstraintOverride_layout_constraintTag 74
int styleable ConstraintOverride_layout_constraintTop_creator 75
int styleable ConstraintOverride_layout_constraintVertical_bias 76
int styleable ConstraintOverride_layout_constraintVertical_chainStyle 77
int styleable ConstraintOverride_layout_constraintVertical_weight 78
int styleable ConstraintOverride_layout_constraintWidth 79
int styleable ConstraintOverride_layout_constraintWidth_default 80
int styleable ConstraintOverride_layout_constraintWidth_max 81
int styleable ConstraintOverride_layout_constraintWidth_min 82
int styleable ConstraintOverride_layout_constraintWidth_percent 83
int styleable ConstraintOverride_layout_editor_absoluteX 84
int styleable ConstraintOverride_layout_editor_absoluteY 85
int styleable ConstraintOverride_layout_goneMarginBaseline 86
int styleable ConstraintOverride_layout_goneMarginBottom 87
int styleable ConstraintOverride_layout_goneMarginEnd 88
int styleable ConstraintOverride_layout_goneMarginLeft 89
int styleable ConstraintOverride_layout_goneMarginRight 90
int styleable ConstraintOverride_layout_goneMarginStart 91
int styleable ConstraintOverride_layout_goneMarginTop 92
int styleable ConstraintOverride_layout_marginBaseline 93
int styleable ConstraintOverride_layout_wrapBehaviorInParent 94
int styleable ConstraintOverride_motionProgress 95
int styleable ConstraintOverride_motionStagger 96
int styleable ConstraintOverride_motionTarget 97
int styleable ConstraintOverride_pathMotionArc 98
int styleable ConstraintOverride_pivotAnchor 99
int styleable ConstraintOverride_polarRelativeTo 100
int styleable ConstraintOverride_quantizeMotionInterpolator 101
int styleable ConstraintOverride_quantizeMotionPhase 102
int styleable ConstraintOverride_quantizeMotionSteps 103
int styleable ConstraintOverride_transformPivotTarget 104
int styleable ConstraintOverride_transitionEasing 105
int styleable ConstraintOverride_transitionPathRotate 106
int styleable ConstraintOverride_visibilityMode 107
int[] styleable ConstraintSet { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010101b5, 0x010101b6, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f02002c, 0x7f02002d, 0x7f020041, 0x7f020042, 0x7f020043, 0x7f020060, 0x7f02007b, 0x7f02007f, 0x7f020080, 0x7f02009e, 0x7f0200aa, 0x7f0200c3, 0x7f0200c4, 0x7f0200c5, 0x7f0200c6, 0x7f0200c7, 0x7f0200c8, 0x7f0200c9, 0x7f0200ca, 0x7f0200cb, 0x7f0200cc, 0x7f0200cd, 0x7f0200ce, 0x7f0200cf, 0x7f0200d1, 0x7f0200d2, 0x7f0200d3, 0x7f0200d4, 0x7f0200d5, 0x7f0200e7, 0x7f020102, 0x7f020103, 0x7f020104, 0x7f020105, 0x7f020106, 0x7f020107, 0x7f020108, 0x7f020109, 0x7f02010a, 0x7f02010b, 0x7f02010c, 0x7f02010d, 0x7f02010e, 0x7f02010f, 0x7f020110, 0x7f020111, 0x7f020112, 0x7f020113, 0x7f020115, 0x7f020116, 0x7f020117, 0x7f020118, 0x7f020119, 0x7f02011a, 0x7f02011b, 0x7f02011c, 0x7f02011d, 0x7f02011e, 0x7f02011f, 0x7f020120, 0x7f020121, 0x7f020122, 0x7f020123, 0x7f020124, 0x7f020125, 0x7f020126, 0x7f020127, 0x7f020128, 0x7f020129, 0x7f02012a, 0x7f02012c, 0x7f02012d, 0x7f02012e, 0x7f02012f, 0x7f020130, 0x7f020131, 0x7f020132, 0x7f020133, 0x7f020134, 0x7f020135, 0x7f020136, 0x7f020137, 0x7f020138, 0x7f020139, 0x7f02013b, 0x7f02016a, 0x7f02016b, 0x7f020187, 0x7f02018e, 0x7f020190, 0x7f020199, 0x7f020217, 0x7f020219 }
int styleable ConstraintSet_android_orientation 0
int styleable ConstraintSet_android_id 1
int styleable ConstraintSet_android_visibility 2
int styleable ConstraintSet_android_layout_width 3
int styleable ConstraintSet_android_layout_height 4
int styleable ConstraintSet_android_layout_marginLeft 5
int styleable ConstraintSet_android_layout_marginTop 6
int styleable ConstraintSet_android_layout_marginRight 7
int styleable ConstraintSet_android_layout_marginBottom 8
int styleable ConstraintSet_android_maxWidth 9
int styleable ConstraintSet_android_maxHeight 10
int styleable ConstraintSet_android_minWidth 11
int styleable ConstraintSet_android_minHeight 12
int styleable ConstraintSet_android_pivotX 13
int styleable ConstraintSet_android_pivotY 14
int styleable ConstraintSet_android_alpha 15
int styleable ConstraintSet_android_transformPivotX 16
int styleable ConstraintSet_android_transformPivotY 17
int styleable ConstraintSet_android_translationX 18
int styleable ConstraintSet_android_translationY 19
int styleable ConstraintSet_android_scaleX 20
int styleable ConstraintSet_android_scaleY 21
int styleable ConstraintSet_android_rotation 22
int styleable ConstraintSet_android_rotationX 23
int styleable ConstraintSet_android_rotationY 24
int styleable ConstraintSet_android_layout_marginStart 25
int styleable ConstraintSet_android_layout_marginEnd 26
int styleable ConstraintSet_android_translationZ 27
int styleable ConstraintSet_android_elevation 28
int styleable ConstraintSet_animateCircleAngleTo 29
int styleable ConstraintSet_animateRelativeTo 30
int styleable ConstraintSet_barrierAllowsGoneWidgets 31
int styleable ConstraintSet_barrierDirection 32
int styleable ConstraintSet_barrierMargin 33
int styleable ConstraintSet_chainUseRtl 34
int styleable ConstraintSet_constraintRotate 35
int styleable ConstraintSet_constraint_referenced_ids 36
int styleable ConstraintSet_constraint_referenced_tags 37
int styleable ConstraintSet_deriveConstraintsFrom 38
int styleable ConstraintSet_drawPath 39
int styleable ConstraintSet_flow_firstHorizontalBias 40
int styleable ConstraintSet_flow_firstHorizontalStyle 41
int styleable ConstraintSet_flow_firstVerticalBias 42
int styleable ConstraintSet_flow_firstVerticalStyle 43
int styleable ConstraintSet_flow_horizontalAlign 44
int styleable ConstraintSet_flow_horizontalBias 45
int styleable ConstraintSet_flow_horizontalGap 46
int styleable ConstraintSet_flow_horizontalStyle 47
int styleable ConstraintSet_flow_lastHorizontalBias 48
int styleable ConstraintSet_flow_lastHorizontalStyle 49
int styleable ConstraintSet_flow_lastVerticalBias 50
int styleable ConstraintSet_flow_lastVerticalStyle 51
int styleable ConstraintSet_flow_maxElementsWrap 52
int styleable ConstraintSet_flow_verticalAlign 53
int styleable ConstraintSet_flow_verticalBias 54
int styleable ConstraintSet_flow_verticalGap 55
int styleable ConstraintSet_flow_verticalStyle 56
int styleable ConstraintSet_flow_wrapMode 57
int styleable ConstraintSet_guidelineUseRtl 58
int styleable ConstraintSet_layout_constrainedHeight 59
int styleable ConstraintSet_layout_constrainedWidth 60
int styleable ConstraintSet_layout_constraintBaseline_creator 61
int styleable ConstraintSet_layout_constraintBaseline_toBaselineOf 62
int styleable ConstraintSet_layout_constraintBaseline_toBottomOf 63
int styleable ConstraintSet_layout_constraintBaseline_toTopOf 64
int styleable ConstraintSet_layout_constraintBottom_creator 65
int styleable ConstraintSet_layout_constraintBottom_toBottomOf 66
int styleable ConstraintSet_layout_constraintBottom_toTopOf 67
int styleable ConstraintSet_layout_constraintCircle 68
int styleable ConstraintSet_layout_constraintCircleAngle 69
int styleable ConstraintSet_layout_constraintCircleRadius 70
int styleable ConstraintSet_layout_constraintDimensionRatio 71
int styleable ConstraintSet_layout_constraintEnd_toEndOf 72
int styleable ConstraintSet_layout_constraintEnd_toStartOf 73
int styleable ConstraintSet_layout_constraintGuide_begin 74
int styleable ConstraintSet_layout_constraintGuide_end 75
int styleable ConstraintSet_layout_constraintGuide_percent 76
int styleable ConstraintSet_layout_constraintHeight_default 77
int styleable ConstraintSet_layout_constraintHeight_max 78
int styleable ConstraintSet_layout_constraintHeight_min 79
int styleable ConstraintSet_layout_constraintHeight_percent 80
int styleable ConstraintSet_layout_constraintHorizontal_bias 81
int styleable ConstraintSet_layout_constraintHorizontal_chainStyle 82
int styleable ConstraintSet_layout_constraintHorizontal_weight 83
int styleable ConstraintSet_layout_constraintLeft_creator 84
int styleable ConstraintSet_layout_constraintLeft_toLeftOf 85
int styleable ConstraintSet_layout_constraintLeft_toRightOf 86
int styleable ConstraintSet_layout_constraintRight_creator 87
int styleable ConstraintSet_layout_constraintRight_toLeftOf 88
int styleable ConstraintSet_layout_constraintRight_toRightOf 89
int styleable ConstraintSet_layout_constraintStart_toEndOf 90
int styleable ConstraintSet_layout_constraintStart_toStartOf 91
int styleable ConstraintSet_layout_constraintTag 92
int styleable ConstraintSet_layout_constraintTop_creator 93
int styleable ConstraintSet_layout_constraintTop_toBottomOf 94
int styleable ConstraintSet_layout_constraintTop_toTopOf 95
int styleable ConstraintSet_layout_constraintVertical_bias 96
int styleable ConstraintSet_layout_constraintVertical_chainStyle 97
int styleable ConstraintSet_layout_constraintVertical_weight 98
int styleable ConstraintSet_layout_constraintWidth_default 99
int styleable ConstraintSet_layout_constraintWidth_max 100
int styleable ConstraintSet_layout_constraintWidth_min 101
int styleable ConstraintSet_layout_constraintWidth_percent 102
int styleable ConstraintSet_layout_editor_absoluteX 103
int styleable ConstraintSet_layout_editor_absoluteY 104
int styleable ConstraintSet_layout_goneMarginBaseline 105
int styleable ConstraintSet_layout_goneMarginBottom 106
int styleable ConstraintSet_layout_goneMarginEnd 107
int styleable ConstraintSet_layout_goneMarginLeft 108
int styleable ConstraintSet_layout_goneMarginRight 109
int styleable ConstraintSet_layout_goneMarginStart 110
int styleable ConstraintSet_layout_goneMarginTop 111
int styleable ConstraintSet_layout_marginBaseline 112
int styleable ConstraintSet_layout_wrapBehaviorInParent 113
int styleable ConstraintSet_motionProgress 114
int styleable ConstraintSet_motionStagger 115
int styleable ConstraintSet_pathMotionArc 116
int styleable ConstraintSet_pivotAnchor 117
int styleable ConstraintSet_polarRelativeTo 118
int styleable ConstraintSet_quantizeMotionSteps 119
int styleable ConstraintSet_transitionEasing 120
int styleable ConstraintSet_transitionPathRotate 121
int[] styleable CustomAttribute { 0x7f020032, 0x7f02008f, 0x7f020090, 0x7f020091, 0x7f020092, 0x7f020093, 0x7f020094, 0x7f020096, 0x7f020097, 0x7f020098, 0x7f020156 }
int styleable CustomAttribute_attributeName 0
int styleable CustomAttribute_customBoolean 1
int styleable CustomAttribute_customColorDrawableValue 2
int styleable CustomAttribute_customColorValue 3
int styleable CustomAttribute_customDimension 4
int styleable CustomAttribute_customFloatValue 5
int styleable CustomAttribute_customIntegerValue 6
int styleable CustomAttribute_customPixelDimension 7
int styleable CustomAttribute_customReference 8
int styleable CustomAttribute_customStringValue 9
int styleable CustomAttribute_methodName 10
int[] styleable DrawerArrowToggle { 0x7f020030, 0x7f020031, 0x7f020040, 0x7f02006f, 0x7f0200af, 0x7f0200e3, 0x7f0201c0, 0x7f0201f7 }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable FontFamily { 0x7f0200d8, 0x7f0200d9, 0x7f0200da, 0x7f0200db, 0x7f0200dc, 0x7f0200dd, 0x7f0200de }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f0200d6, 0x7f0200df, 0x7f0200e0, 0x7f0200e1, 0x7f02021d }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable GPUImageView { 0x7f0200e5, 0x7f0200e6 }
int styleable GPUImageView_gpuimage_show_loading 0
int styleable GPUImageView_gpuimage_surface_type 1
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable ImageFilterView { 0x7f02002b, 0x7f020044, 0x7f020048, 0x7f02008a, 0x7f02008c, 0x7f0200f3, 0x7f0200f4, 0x7f0200f5, 0x7f0200f6, 0x7f02017f, 0x7f0201ac, 0x7f0201ad, 0x7f0201ae, 0x7f020226 }
int styleable ImageFilterView_altSrc 0
int styleable ImageFilterView_blendSrc 1
int styleable ImageFilterView_brightness 2
int styleable ImageFilterView_contrast 3
int styleable ImageFilterView_crossfade 4
int styleable ImageFilterView_imagePanX 5
int styleable ImageFilterView_imagePanY 6
int styleable ImageFilterView_imageRotate 7
int styleable ImageFilterView_imageZoom 8
int styleable ImageFilterView_overlay 9
int styleable ImageFilterView_round 10
int styleable ImageFilterView_roundPercent 11
int styleable ImageFilterView_saturation 12
int styleable ImageFilterView_warmth 13
int[] styleable KeyAttribute { 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f02008e, 0x7f0200e2, 0x7f02016a, 0x7f02016c, 0x7f020215, 0x7f020217, 0x7f020219 }
int styleable KeyAttribute_android_alpha 0
int styleable KeyAttribute_android_transformPivotX 1
int styleable KeyAttribute_android_transformPivotY 2
int styleable KeyAttribute_android_translationX 3
int styleable KeyAttribute_android_translationY 4
int styleable KeyAttribute_android_scaleX 5
int styleable KeyAttribute_android_scaleY 6
int styleable KeyAttribute_android_rotation 7
int styleable KeyAttribute_android_rotationX 8
int styleable KeyAttribute_android_rotationY 9
int styleable KeyAttribute_android_translationZ 10
int styleable KeyAttribute_android_elevation 11
int styleable KeyAttribute_curveFit 12
int styleable KeyAttribute_framePosition 13
int styleable KeyAttribute_motionProgress 14
int styleable KeyAttribute_motionTarget 15
int styleable KeyAttribute_transformPivotTarget 16
int styleable KeyAttribute_transitionEasing 17
int styleable KeyAttribute_transitionPathRotate 18
int[] styleable KeyCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f02008e, 0x7f0200e2, 0x7f02016a, 0x7f02016c, 0x7f020217, 0x7f020219, 0x7f020228, 0x7f020229, 0x7f02022a, 0x7f02022b, 0x7f02022c }
int styleable KeyCycle_android_alpha 0
int styleable KeyCycle_android_translationX 1
int styleable KeyCycle_android_translationY 2
int styleable KeyCycle_android_scaleX 3
int styleable KeyCycle_android_scaleY 4
int styleable KeyCycle_android_rotation 5
int styleable KeyCycle_android_rotationX 6
int styleable KeyCycle_android_rotationY 7
int styleable KeyCycle_android_translationZ 8
int styleable KeyCycle_android_elevation 9
int styleable KeyCycle_curveFit 10
int styleable KeyCycle_framePosition 11
int styleable KeyCycle_motionProgress 12
int styleable KeyCycle_motionTarget 13
int styleable KeyCycle_transitionEasing 14
int styleable KeyCycle_transitionPathRotate 15
int styleable KeyCycle_waveOffset 16
int styleable KeyCycle_wavePeriod 17
int styleable KeyCycle_wavePhase 18
int styleable KeyCycle_waveShape 19
int styleable KeyCycle_waveVariesBy 20
int[] styleable KeyFrame { }
int[] styleable KeyFramesAcceleration { }
int[] styleable KeyFramesVelocity { }
int[] styleable KeyPosition { 0x7f02008e, 0x7f0200aa, 0x7f0200e2, 0x7f0200fb, 0x7f02016c, 0x7f020187, 0x7f020189, 0x7f02018a, 0x7f02018b, 0x7f02018c, 0x7f0201be, 0x7f020217 }
int styleable KeyPosition_curveFit 0
int styleable KeyPosition_drawPath 1
int styleable KeyPosition_framePosition 2
int styleable KeyPosition_keyPositionType 3
int styleable KeyPosition_motionTarget 4
int styleable KeyPosition_pathMotionArc 5
int styleable KeyPosition_percentHeight 6
int styleable KeyPosition_percentWidth 7
int styleable KeyPosition_percentX 8
int styleable KeyPosition_percentY 9
int styleable KeyPosition_sizePercent 10
int styleable KeyPosition_transitionEasing 11
int[] styleable KeyTimeCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f02008e, 0x7f0200e2, 0x7f02016a, 0x7f02016c, 0x7f020217, 0x7f020219, 0x7f020227, 0x7f020228, 0x7f020229, 0x7f02022a, 0x7f02022b }
int styleable KeyTimeCycle_android_alpha 0
int styleable KeyTimeCycle_android_translationX 1
int styleable KeyTimeCycle_android_translationY 2
int styleable KeyTimeCycle_android_scaleX 3
int styleable KeyTimeCycle_android_scaleY 4
int styleable KeyTimeCycle_android_rotation 5
int styleable KeyTimeCycle_android_rotationX 6
int styleable KeyTimeCycle_android_rotationY 7
int styleable KeyTimeCycle_android_translationZ 8
int styleable KeyTimeCycle_android_elevation 9
int styleable KeyTimeCycle_curveFit 10
int styleable KeyTimeCycle_framePosition 11
int styleable KeyTimeCycle_motionProgress 12
int styleable KeyTimeCycle_motionTarget 13
int styleable KeyTimeCycle_transitionEasing 14
int styleable KeyTimeCycle_transitionPathRotate 15
int styleable KeyTimeCycle_waveDecay 16
int styleable KeyTimeCycle_waveOffset 17
int styleable KeyTimeCycle_wavePeriod 18
int styleable KeyTimeCycle_wavePhase 19
int styleable KeyTimeCycle_waveShape 20
int[] styleable KeyTrigger { 0x7f0200e2, 0x7f02016c, 0x7f02016d, 0x7f02016e, 0x7f020177, 0x7f020179, 0x7f02017a, 0x7f02021a, 0x7f02021b, 0x7f02021c, 0x7f020221, 0x7f020222, 0x7f020223 }
int styleable KeyTrigger_framePosition 0
int styleable KeyTrigger_motionTarget 1
int styleable KeyTrigger_motion_postLayoutCollision 2
int styleable KeyTrigger_motion_triggerOnCollision 3
int styleable KeyTrigger_onCross 4
int styleable KeyTrigger_onNegativeCross 5
int styleable KeyTrigger_onPositiveCross 6
int styleable KeyTrigger_triggerId 7
int styleable KeyTrigger_triggerReceiver 8
int styleable KeyTrigger_triggerSlack 9
int styleable KeyTrigger_viewTransitionOnCross 10
int styleable KeyTrigger_viewTransitionOnNegativeCross 11
int styleable KeyTrigger_viewTransitionOnPositiveCross 12
int[] styleable Layout { 0x010100c4, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x010103b5, 0x010103b6, 0x7f020041, 0x7f020042, 0x7f020043, 0x7f020060, 0x7f02007f, 0x7f020080, 0x7f0200e7, 0x7f020102, 0x7f020103, 0x7f020104, 0x7f020105, 0x7f020106, 0x7f020107, 0x7f020108, 0x7f020109, 0x7f02010a, 0x7f02010b, 0x7f02010c, 0x7f02010d, 0x7f02010e, 0x7f02010f, 0x7f020110, 0x7f020111, 0x7f020112, 0x7f020113, 0x7f020114, 0x7f020115, 0x7f020116, 0x7f020117, 0x7f020118, 0x7f020119, 0x7f02011a, 0x7f02011b, 0x7f02011c, 0x7f02011d, 0x7f02011e, 0x7f02011f, 0x7f020120, 0x7f020121, 0x7f020122, 0x7f020123, 0x7f020125, 0x7f020126, 0x7f020127, 0x7f020128, 0x7f020129, 0x7f02012a, 0x7f02012b, 0x7f02012c, 0x7f02012d, 0x7f02012e, 0x7f02012f, 0x7f020130, 0x7f020131, 0x7f020132, 0x7f020133, 0x7f020134, 0x7f020135, 0x7f020136, 0x7f020137, 0x7f020138, 0x7f020139, 0x7f02013b, 0x7f020151, 0x7f020153, 0x7f020157, 0x7f020158 }
int styleable Layout_android_orientation 0
int styleable Layout_android_layout_width 1
int styleable Layout_android_layout_height 2
int styleable Layout_android_layout_marginLeft 3
int styleable Layout_android_layout_marginTop 4
int styleable Layout_android_layout_marginRight 5
int styleable Layout_android_layout_marginBottom 6
int styleable Layout_android_layout_marginStart 7
int styleable Layout_android_layout_marginEnd 8
int styleable Layout_barrierAllowsGoneWidgets 9
int styleable Layout_barrierDirection 10
int styleable Layout_barrierMargin 11
int styleable Layout_chainUseRtl 12
int styleable Layout_constraint_referenced_ids 13
int styleable Layout_constraint_referenced_tags 14
int styleable Layout_guidelineUseRtl 15
int styleable Layout_layout_constrainedHeight 16
int styleable Layout_layout_constrainedWidth 17
int styleable Layout_layout_constraintBaseline_creator 18
int styleable Layout_layout_constraintBaseline_toBaselineOf 19
int styleable Layout_layout_constraintBaseline_toBottomOf 20
int styleable Layout_layout_constraintBaseline_toTopOf 21
int styleable Layout_layout_constraintBottom_creator 22
int styleable Layout_layout_constraintBottom_toBottomOf 23
int styleable Layout_layout_constraintBottom_toTopOf 24
int styleable Layout_layout_constraintCircle 25
int styleable Layout_layout_constraintCircleAngle 26
int styleable Layout_layout_constraintCircleRadius 27
int styleable Layout_layout_constraintDimensionRatio 28
int styleable Layout_layout_constraintEnd_toEndOf 29
int styleable Layout_layout_constraintEnd_toStartOf 30
int styleable Layout_layout_constraintGuide_begin 31
int styleable Layout_layout_constraintGuide_end 32
int styleable Layout_layout_constraintGuide_percent 33
int styleable Layout_layout_constraintHeight 34
int styleable Layout_layout_constraintHeight_default 35
int styleable Layout_layout_constraintHeight_max 36
int styleable Layout_layout_constraintHeight_min 37
int styleable Layout_layout_constraintHeight_percent 38
int styleable Layout_layout_constraintHorizontal_bias 39
int styleable Layout_layout_constraintHorizontal_chainStyle 40
int styleable Layout_layout_constraintHorizontal_weight 41
int styleable Layout_layout_constraintLeft_creator 42
int styleable Layout_layout_constraintLeft_toLeftOf 43
int styleable Layout_layout_constraintLeft_toRightOf 44
int styleable Layout_layout_constraintRight_creator 45
int styleable Layout_layout_constraintRight_toLeftOf 46
int styleable Layout_layout_constraintRight_toRightOf 47
int styleable Layout_layout_constraintStart_toEndOf 48
int styleable Layout_layout_constraintStart_toStartOf 49
int styleable Layout_layout_constraintTop_creator 50
int styleable Layout_layout_constraintTop_toBottomOf 51
int styleable Layout_layout_constraintTop_toTopOf 52
int styleable Layout_layout_constraintVertical_bias 53
int styleable Layout_layout_constraintVertical_chainStyle 54
int styleable Layout_layout_constraintVertical_weight 55
int styleable Layout_layout_constraintWidth 56
int styleable Layout_layout_constraintWidth_default 57
int styleable Layout_layout_constraintWidth_max 58
int styleable Layout_layout_constraintWidth_min 59
int styleable Layout_layout_constraintWidth_percent 60
int styleable Layout_layout_editor_absoluteX 61
int styleable Layout_layout_editor_absoluteY 62
int styleable Layout_layout_goneMarginBaseline 63
int styleable Layout_layout_goneMarginBottom 64
int styleable Layout_layout_goneMarginEnd 65
int styleable Layout_layout_goneMarginLeft 66
int styleable Layout_layout_goneMarginRight 67
int styleable Layout_layout_goneMarginStart 68
int styleable Layout_layout_goneMarginTop 69
int styleable Layout_layout_marginBaseline 70
int styleable Layout_layout_wrapBehaviorInParent 71
int styleable Layout_maxHeight 72
int styleable Layout_maxWidth 73
int styleable Layout_minHeight 74
int styleable Layout_minWidth 75
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f0200a3, 0x7f0200a5, 0x7f020154, 0x7f0201b9 }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f02000f, 0x7f020021, 0x7f020022, 0x7f02002a, 0x7f020083, 0x7f0200ed, 0x7f0200ee, 0x7f020176, 0x7f0201b8, 0x7f02020e }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f020194, 0x7f0201cd }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable MockView { 0x7f020159, 0x7f02015a, 0x7f02015b, 0x7f02015c, 0x7f02015d, 0x7f02015e }
int styleable MockView_mock_diagonalsColor 0
int styleable MockView_mock_label 1
int styleable MockView_mock_labelBackgroundColor 2
int styleable MockView_mock_labelColor 3
int styleable MockView_mock_showDiagonals 4
int styleable MockView_mock_showLabel 5
int[] styleable Motion { 0x7f02002c, 0x7f02002d, 0x7f0200aa, 0x7f020169, 0x7f02016b, 0x7f020187, 0x7f020197, 0x7f020198, 0x7f020199, 0x7f020217 }
int styleable Motion_animateCircleAngleTo 0
int styleable Motion_animateRelativeTo 1
int styleable Motion_drawPath 2
int styleable Motion_motionPathRotate 3
int styleable Motion_motionStagger 4
int styleable Motion_pathMotionArc 5
int styleable Motion_quantizeMotionInterpolator 6
int styleable Motion_quantizeMotionPhase 7
int styleable Motion_quantizeMotionSteps 8
int styleable Motion_transitionEasing 9
int[] styleable MotionEffect { 0x7f020160, 0x7f020161, 0x7f020162, 0x7f020163, 0x7f020164, 0x7f020165, 0x7f020166, 0x7f020167 }
int styleable MotionEffect_motionEffect_alpha 0
int styleable MotionEffect_motionEffect_end 1
int styleable MotionEffect_motionEffect_move 2
int styleable MotionEffect_motionEffect_start 3
int styleable MotionEffect_motionEffect_strict 4
int styleable MotionEffect_motionEffect_translationX 5
int styleable MotionEffect_motionEffect_translationY 6
int styleable MotionEffect_motionEffect_viewTransition 7
int[] styleable MotionHelper { 0x7f020178, 0x7f02017b }
int styleable MotionHelper_onHide 0
int styleable MotionHelper_onShow 1
int[] styleable MotionLabel { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x010100af, 0x0101014f, 0x01010164, 0x010103ac, 0x01010535, 0x7f020045, 0x7f020046, 0x7f0201af, 0x7f0201e5, 0x7f0201e6, 0x7f0201e7, 0x7f0201e8, 0x7f0201e9, 0x7f0201ee, 0x7f0201ef, 0x7f0201f0, 0x7f0201f1, 0x7f0201f2, 0x7f0201f3, 0x7f0201f4, 0x7f0201f5 }
int styleable MotionLabel_android_textSize 0
int styleable MotionLabel_android_typeface 1
int styleable MotionLabel_android_textStyle 2
int styleable MotionLabel_android_textColor 3
int styleable MotionLabel_android_gravity 4
int styleable MotionLabel_android_text 5
int styleable MotionLabel_android_shadowRadius 6
int styleable MotionLabel_android_fontFamily 7
int styleable MotionLabel_android_autoSizeTextType 8
int styleable MotionLabel_borderRound 9
int styleable MotionLabel_borderRoundPercent 10
int styleable MotionLabel_scaleFromTextSize 11
int styleable MotionLabel_textBackground 12
int styleable MotionLabel_textBackgroundPanX 13
int styleable MotionLabel_textBackgroundPanY 14
int styleable MotionLabel_textBackgroundRotate 15
int styleable MotionLabel_textBackgroundZoom 16
int styleable MotionLabel_textOutlineColor 17
int styleable MotionLabel_textOutlineThickness 18
int styleable MotionLabel_textPanX 19
int styleable MotionLabel_textPanY 20
int styleable MotionLabel_textureBlurFactor 21
int styleable MotionLabel_textureEffect 22
int styleable MotionLabel_textureHeight 23
int styleable MotionLabel_textureWidth 24
int[] styleable MotionLayout { 0x7f02002e, 0x7f02008d, 0x7f0200ff, 0x7f02015f, 0x7f02016a, 0x7f0201ba }
int styleable MotionLayout_applyMotionScene 0
int styleable MotionLayout_currentState 1
int styleable MotionLayout_layoutDescription 2
int styleable MotionLayout_motionDebug 3
int styleable MotionLayout_motionProgress 4
int styleable MotionLayout_showPaths 5
int[] styleable MotionScene { 0x7f020099, 0x7f020100 }
int styleable MotionScene_defaultDuration 0
int styleable MotionScene_layoutDuringTransition 1
int[] styleable MotionTelltales { 0x7f0201d9, 0x7f0201da, 0x7f0201db }
int styleable MotionTelltales_telltales_tailColor 0
int styleable MotionTelltales_telltales_tailScale 1
int styleable MotionTelltales_telltales_velocityMode 2
int[] styleable OnClick { 0x7f02006a, 0x7f0201d8 }
int styleable OnClick_clickAction 0
int styleable OnClick_targetId 1
int[] styleable OnSwipe { 0x7f020033, 0x7f0200a7, 0x7f0200a8, 0x7f0200a9, 0x7f02013c, 0x7f02014f, 0x7f020152, 0x7f02016f, 0x7f020174, 0x7f02017d, 0x7f0201ab, 0x7f0201c4, 0x7f0201c5, 0x7f0201c6, 0x7f0201c7, 0x7f0201c8, 0x7f02020f, 0x7f020210, 0x7f020211 }
int styleable OnSwipe_autoCompleteMode 0
int styleable OnSwipe_dragDirection 1
int styleable OnSwipe_dragScale 2
int styleable OnSwipe_dragThreshold 3
int styleable OnSwipe_limitBoundsTo 4
int styleable OnSwipe_maxAcceleration 5
int styleable OnSwipe_maxVelocity 6
int styleable OnSwipe_moveWhenScrollAtTop 7
int styleable OnSwipe_nestedScrollFlags 8
int styleable OnSwipe_onTouchUp 9
int styleable OnSwipe_rotationCenterId 10
int styleable OnSwipe_springBoundary 11
int styleable OnSwipe_springDamping 12
int styleable OnSwipe_springMass 13
int styleable OnSwipe_springStiffness 14
int styleable OnSwipe_springStopThreshold 15
int styleable OnSwipe_touchAnchorId 16
int styleable OnSwipe_touchAnchorSide 17
int styleable OnSwipe_touchRegionId 18
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f02017e }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f0201cc }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable PropertySet { 0x010100dc, 0x0101031f, 0x7f020124, 0x7f02016a, 0x7f020224 }
int styleable PropertySet_android_visibility 0
int styleable PropertySet_android_alpha 1
int styleable PropertySet_layout_constraintTag 2
int styleable PropertySet_motionProgress 3
int styleable PropertySet_visibilityMode 4
int[] styleable RecycleListView { 0x7f020180, 0x7f020183 }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f0200bd, 0x7f0200be, 0x7f0200bf, 0x7f0200c0, 0x7f0200c1, 0x7f020101, 0x7f0201aa, 0x7f0201bf, 0x7f0201ca }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_clipToPadding 1
int styleable RecyclerView_android_descendantFocusability 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable SearchView { 0x010100da, 0x0101011f, 0x01010220, 0x01010264, 0x7f02006b, 0x7f02007a, 0x7f02009a, 0x7f0200e4, 0x7f0200ef, 0x7f0200fe, 0x7f02019a, 0x7f02019b, 0x7f0201b0, 0x7f0201b1, 0x7f0201ce, 0x7f0201d3, 0x7f020225 }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_maxWidth 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_imeOptions 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f020192 }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable State { 0x010100d0, 0x7f020081 }
int styleable State_android_id 0
int styleable State_constraints 1
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable StateSet { 0x7f02009b }
int styleable StateSet_defaultState 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f0201bb, 0x7f0201c3, 0x7f0201d4, 0x7f0201d5, 0x7f0201d7, 0x7f0201f8, 0x7f0201f9, 0x7f0201fa, 0x7f020212, 0x7f020213, 0x7f020214 }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f0200d7, 0x7f0200e0, 0x7f0201dc, 0x7f0201ed }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_android_textFontWeight 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable TextEffects { 0x01010095, 0x01010096, 0x01010097, 0x0101014f, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x7f020045, 0x7f020046, 0x7f0201ec, 0x7f0201ee, 0x7f0201ef }
int styleable TextEffects_android_textSize 0
int styleable TextEffects_android_typeface 1
int styleable TextEffects_android_textStyle 2
int styleable TextEffects_android_text 3
int styleable TextEffects_android_shadowColor 4
int styleable TextEffects_android_shadowDx 5
int styleable TextEffects_android_shadowDy 6
int styleable TextEffects_android_shadowRadius 7
int styleable TextEffects_android_fontFamily 8
int styleable TextEffects_borderRound 9
int styleable TextEffects_borderRoundPercent 10
int styleable TextEffects_textFillColor 11
int styleable TextEffects_textOutlineColor 12
int styleable TextEffects_textOutlineThickness 13
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f02004f, 0x7f02006d, 0x7f02006e, 0x7f020084, 0x7f020085, 0x7f020086, 0x7f020087, 0x7f020088, 0x7f020089, 0x7f02014d, 0x7f02014e, 0x7f020150, 0x7f020155, 0x7f020171, 0x7f020172, 0x7f020192, 0x7f0201cf, 0x7f0201d0, 0x7f0201d1, 0x7f020200, 0x7f020201, 0x7f020202, 0x7f020203, 0x7f020204, 0x7f020205, 0x7f020206, 0x7f020207, 0x7f020208 }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 25
int styleable Toolbar_titleMarginTop 26
int styleable Toolbar_titleMargins 27
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable Transform { 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f020215 }
int styleable Transform_android_transformPivotX 0
int styleable Transform_android_transformPivotY 1
int styleable Transform_android_translationX 2
int styleable Transform_android_translationY 3
int styleable Transform_android_scaleX 4
int styleable Transform_android_scaleY 5
int styleable Transform_android_rotation 6
int styleable Transform_android_rotationX 7
int styleable Transform_android_rotationY 8
int styleable Transform_android_translationZ 9
int styleable Transform_android_elevation 10
int styleable Transform_transformPivotTarget 11
int[] styleable Transition { 0x010100d0, 0x7f02003a, 0x7f02007d, 0x7f02007e, 0x7f0200b7, 0x7f020100, 0x7f020168, 0x7f020187, 0x7f0201cb, 0x7f020216, 0x7f020218 }
int styleable Transition_android_id 0
int styleable Transition_autoTransition 1
int styleable Transition_constraintSetEnd 2
int styleable Transition_constraintSetStart 3
int styleable Transition_duration 4
int styleable Transition_layoutDuringTransition 5
int styleable Transition_motionInterpolator 6
int styleable Transition_pathMotionArc 7
int styleable Transition_staggered 8
int styleable Transition_transitionDisable 9
int styleable Transition_transitionFlags 10
int[] styleable Variant { 0x7f020081, 0x7f0201a6, 0x7f0201a7, 0x7f0201a8, 0x7f0201a9 }
int styleable Variant_constraints 0
int styleable Variant_region_heightLessThan 1
int styleable Variant_region_heightMoreThan 2
int styleable Variant_region_widthLessThan 3
int styleable Variant_region_widthMoreThan 4
int[] styleable View { 0x01010000, 0x010100da, 0x7f020181, 0x7f020182, 0x7f0201f6 }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f02003e, 0x7f02003f }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int[] styleable ViewTransition { 0x010100d0, 0x7f020000, 0x7f020001, 0x7f020069, 0x7f0200b7, 0x7f0200f0, 0x7f0200f1, 0x7f020168, 0x7f02016c, 0x7f02017c, 0x7f020187, 0x7f0201b6, 0x7f020216, 0x7f02021e, 0x7f020220 }
int styleable ViewTransition_android_id 0
int styleable ViewTransition_SharedValue 1
int styleable ViewTransition_SharedValueId 2
int styleable ViewTransition_clearsTag 3
int styleable ViewTransition_duration 4
int styleable ViewTransition_ifTagNotSet 5
int styleable ViewTransition_ifTagSet 6
int styleable ViewTransition_motionInterpolator 7
int styleable ViewTransition_motionTarget 8
int styleable ViewTransition_onStateTransition 9
int styleable ViewTransition_pathMotionArc 10
int styleable ViewTransition_setsTag 11
int styleable ViewTransition_transitionDisable 12
int styleable ViewTransition_upDuration 13
int styleable ViewTransition_viewTransitionMode 14
int[] styleable include { 0x7f02007c }
int styleable include_constraintSet 0
