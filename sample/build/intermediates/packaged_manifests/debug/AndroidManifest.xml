<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="jp.co.cyberagent.android.gpuimage.sample"
    android:versionCode="14"
    android:versionName="2.1.0" >

    <uses-sdk
        android:minSdkVersion="14"
        android:targetSdkVersion="33" />

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- For images from picasa -->
    <uses-permission android:name="android.permission.INTERNET" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />

    <application
        android:allowBackup="false"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:hardwareAccelerated="true"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:theme="@style/AppTheme" >
        <activity
            android:name="jp.co.cyberagent.android.gpuimage.sample.activity.MainActivity"
            android:exported="true"
            android:label="@string/title_activity_activity_main" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name="jp.co.cyberagent.android.gpuimage.sample.activity.GalleryActivity" />
        <activity
            android:name="jp.co.cyberagent.android.gpuimage.sample.activity.CameraActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
    </application>

</manifest>