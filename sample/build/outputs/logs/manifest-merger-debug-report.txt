-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-41:12
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-41:12
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-41:12
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-41:12
MERGED from [:library] /Users/<USER>/Desktop/git/android-gpuimage/library/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/b9a730ee48cf7175e58aa4353be64681/constraintlayout-2.1.4/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.2.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/2f92b9b7fa7d26b98742c9b2a7b8abdc/appcompat-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/9475f27a761a8b23d437709fed75dc42/recyclerview-1.3.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/066486521b022a2b86561843cca39c55/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/d33d27834487a6a48a98eb0482314b16/fragment-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/c05f12ec6bbad0d9095188d27be7f920/jetified-appcompat-resources-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/c42e5ee5d7efa62607624f85ed877305/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/05aa9b51c666ee5a27ef90cf711599fd/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/ea41f6da84fc01dd87225cef86d5bddf/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/057016ed93e3f76cecb239e09136ae5d/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/148db2bf531aa476d57edfabe5f99ce7/jetified-activity-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/30e728773d2ddd34c2ce6b3b0693b468/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/193f93ea8b6bcbd026cfbcfa9aa714ee/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/ec5aac60b235e17d40050b661abab6b1/jetified-customview-poolingcontainer-1.0.0/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.5.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/0ac9532ae1dea4ce6db21a6206171072/jetified-core-ktx-1.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.core:core:1.7.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8da633473d9eb84c16abee55877a4f30/core-1.7.0/AndroidManifest.xml:17:1-26:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/91436f3103b138fb3f5f7a65f1c02d17/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/81c989234ec0ba3b7d61065a80c8aa80/lifecycle-runtime-2.3.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/4ba2c4ab6434f2c0c25cfef3eb267892/lifecycle-viewmodel-2.1.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/49c9b3920472e1f78e3763cf08e935f0/jetified-savedstate-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/1ee1e79ea9f624b86e22f3a91521c7f2/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/c7a731d394afdf52ff70027224e295e1/lifecycle-livedata-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/25f68a533c6ab62ea5f45ecfe04ac7b6/lifecycle-livedata-core-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/3b96f6e244aa87c0c092ae456ee5fd27/core-runtime-2.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/e9a5cff0217e2fac5e9d0ab93d0a0de8/jetified-annotation-experimental-1.1.0/AndroidManifest.xml:17:1-24:12
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-41:12
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-41:12
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-41:12
	package
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:3:5-55
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-41:12
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-41:12
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:5:5-81
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:5:22-78
uses-permission#android.permission.CAMERA
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:6:5-65
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:6:22-62
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:9:5-67
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:9:22-64
uses-feature#android.hardware.camera
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:11:5-60
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:11:19-57
uses-feature#android.hardware.camera.autofocus
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:12:5-14:36
	android:required
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:14:9-33
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:13:9-57
application
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:16:5-39:19
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/b9a730ee48cf7175e58aa4353be64681/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/b9a730ee48cf7175e58aa4353be64681/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.core:core:1.7.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8da633473d9eb84c16abee55877a4f30/core-1.7.0/AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.7.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8da633473d9eb84c16abee55877a4f30/core-1.7.0/AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/91436f3103b138fb3f5f7a65f1c02d17/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/91436f3103b138fb3f5f7a65f1c02d17/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.7.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8da633473d9eb84c16abee55877a4f30/core-1.7.0/AndroidManifest.xml:24:18-86
	android:label
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:20:9-41
	android:hardwareAccelerated
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:18:9-43
	tools:ignore
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:23:9-48
	android:largeHeap
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:21:9-33
	android:icon
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:19:9-45
	android:allowBackup
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:17:9-36
	android:theme
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:22:9-40
activity#jp.co.cyberagent.android.gpuimage.sample.activity.MainActivity
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:24:9-33:20
	android:label
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:26:13-65
	android:exported
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:27:13-36
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:25:13-50
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:28:13-32:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:29:17-69
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:29:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:31:17-77
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:31:27-74
activity#jp.co.cyberagent.android.gpuimage.sample.activity.GalleryActivity
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:34:9-62
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:34:19-59
activity#jp.co.cyberagent.android.gpuimage.sample.activity.CameraActivity
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:35:9-38:59
	android:screenOrientation
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:37:13-49
	android:theme
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:38:13-56
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:36:13-52
uses-sdk
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
MERGED from [:library] /Users/<USER>/Desktop/git/android-gpuimage/library/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:library] /Users/<USER>/Desktop/git/android-gpuimage/library/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/b9a730ee48cf7175e58aa4353be64681/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/b9a730ee48cf7175e58aa4353be64681/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.2.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/2f92b9b7fa7d26b98742c9b2a7b8abdc/appcompat-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/2f92b9b7fa7d26b98742c9b2a7b8abdc/appcompat-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.2] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/9475f27a761a8b23d437709fed75dc42/recyclerview-1.3.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/9475f27a761a8b23d437709fed75dc42/recyclerview-1.3.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/066486521b022a2b86561843cca39c55/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/066486521b022a2b86561843cca39c55/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/d33d27834487a6a48a98eb0482314b16/fragment-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/d33d27834487a6a48a98eb0482314b16/fragment-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/c05f12ec6bbad0d9095188d27be7f920/jetified-appcompat-resources-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/c05f12ec6bbad0d9095188d27be7f920/jetified-appcompat-resources-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/c42e5ee5d7efa62607624f85ed877305/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/c42e5ee5d7efa62607624f85ed877305/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/05aa9b51c666ee5a27ef90cf711599fd/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/05aa9b51c666ee5a27ef90cf711599fd/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/ea41f6da84fc01dd87225cef86d5bddf/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/ea41f6da84fc01dd87225cef86d5bddf/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/057016ed93e3f76cecb239e09136ae5d/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/057016ed93e3f76cecb239e09136ae5d/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/148db2bf531aa476d57edfabe5f99ce7/jetified-activity-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/148db2bf531aa476d57edfabe5f99ce7/jetified-activity-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/30e728773d2ddd34c2ce6b3b0693b468/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/30e728773d2ddd34c2ce6b3b0693b468/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/193f93ea8b6bcbd026cfbcfa9aa714ee/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/193f93ea8b6bcbd026cfbcfa9aa714ee/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/ec5aac60b235e17d40050b661abab6b1/jetified-customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/ec5aac60b235e17d40050b661abab6b1/jetified-customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.5.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/0ac9532ae1dea4ce6db21a6206171072/jetified-core-ktx-1.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core-ktx:1.5.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/0ac9532ae1dea4ce6db21a6206171072/jetified-core-ktx-1.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core:1.7.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8da633473d9eb84c16abee55877a4f30/core-1.7.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.7.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8da633473d9eb84c16abee55877a4f30/core-1.7.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/91436f3103b138fb3f5f7a65f1c02d17/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/91436f3103b138fb3f5f7a65f1c02d17/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/81c989234ec0ba3b7d61065a80c8aa80/lifecycle-runtime-2.3.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.1] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/81c989234ec0ba3b7d61065a80c8aa80/lifecycle-runtime-2.3.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/4ba2c4ab6434f2c0c25cfef3eb267892/lifecycle-viewmodel-2.1.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/4ba2c4ab6434f2c0c25cfef3eb267892/lifecycle-viewmodel-2.1.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/49c9b3920472e1f78e3763cf08e935f0/jetified-savedstate-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/49c9b3920472e1f78e3763cf08e935f0/jetified-savedstate-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/1ee1e79ea9f624b86e22f3a91521c7f2/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/1ee1e79ea9f624b86e22f3a91521c7f2/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/c7a731d394afdf52ff70027224e295e1/lifecycle-livedata-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/c7a731d394afdf52ff70027224e295e1/lifecycle-livedata-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/25f68a533c6ab62ea5f45ecfe04ac7b6/lifecycle-livedata-core-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/25f68a533c6ab62ea5f45ecfe04ac7b6/lifecycle-livedata-core-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/3b96f6e244aa87c0c092ae456ee5fd27/core-runtime-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/3b96f6e244aa87c0c092ae456ee5fd27/core-runtime-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/e9a5cff0217e2fac5e9d0ab93d0a0de8/jetified-annotation-experimental-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/e9a5cff0217e2fac5e9d0ab93d0a0de8/jetified-annotation-experimental-1.1.0/AndroidManifest.xml:20:5-22:41
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
