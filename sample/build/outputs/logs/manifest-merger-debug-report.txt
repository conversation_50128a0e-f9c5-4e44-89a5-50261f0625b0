-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-43:12
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-43:12
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-43:12
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-43:12
MERGED from [:library] /Users/<USER>/Desktop/git/android-gpuimage/library/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat:1.2.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/10b15c911195a22935b5370f26e9e44c/appcompat-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/d64e89d8f607115f011738cc3440d5b7/fragment-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/cd063c0dcf54faba3417c17936476171/jetified-appcompat-resources-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/cb7eb146fe69c26b5c43254819d27294/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/80ecf929044b86a5aa46915aa2d2281b/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8f3b96250de423d5be9b5ca48356f5fb/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8ee76296a2a1da1e044b91eff7400b45/jetified-activity-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/b030ea72b339535608db7596e5689c4b/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/e0109c0ed32efcd5b74be2da13868ff0/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/705870a69888600444d749c96bebe43d/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.3.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/22e35acf320ba1436f8703b3b8ba64f7/core-1.3.0/AndroidManifest.xml:17:1-26:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/71fab9291d2a8d398a1f9cad8ad8949f/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/efd12a819431f8eb8701e58f73a88386/versionedparcelable-1.1.0/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/e6fe80f301a425e9379179f5944495ef/lifecycle-runtime-2.1.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/ccd8403c623e9f66e3369083a163e0c2/lifecycle-viewmodel-2.1.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/b39b0582f616dfbe0cfd92a4661830cb/jetified-savedstate-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/58fd9af7829cf82f3521079d8c90d4a8/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/44e6af7b037a2017d10299299b85d40f/lifecycle-livedata-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/b71fb64951ec116280d0ca535ff82573/lifecycle-livedata-core-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/1358fab37b79a0d95eb63d7bd7398e8e/core-runtime-2.0.0/AndroidManifest.xml:17:1-22:12
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-43:12
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-43:12
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-43:12
	package
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:3:5-55
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-43:12
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:1-43:12
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:5:5-81
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:5:22-78
uses-permission#android.permission.CAMERA
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:6:5-65
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:6:22-62
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:9:5-67
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:9:22-64
uses-feature#android.hardware.camera
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:11:5-60
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:11:19-57
uses-feature#android.hardware.camera.autofocus
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:12:5-14:36
	android:required
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:14:9-33
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:13:9-57
application
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:16:5-41:19
MERGED from [androidx.core:core:1.3.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/22e35acf320ba1436f8703b3b8ba64f7/core-1.3.0/AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.3.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/22e35acf320ba1436f8703b3b8ba64f7/core-1.3.0/AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/efd12a819431f8eb8701e58f73a88386/versionedparcelable-1.1.0/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/efd12a819431f8eb8701e58f73a88386/versionedparcelable-1.1.0/AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.3.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/22e35acf320ba1436f8703b3b8ba64f7/core-1.3.0/AndroidManifest.xml:24:18-86
	android:label
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:20:9-41
	android:hardwareAccelerated
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:18:9-43
	tools:ignore
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:23:9-48
	android:largeHeap
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:21:9-33
	android:icon
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:19:9-45
	android:allowBackup
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:17:9-36
	android:theme
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:22:9-40
activity#jp.co.cyberagent.android.gpuimage.sample.activity.MainActivity
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:24:9-32:20
	android:label
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:26:13-65
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:25:13-50
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:27:13-31:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:28:17-69
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:28:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:30:17-77
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:30:27-74
activity#jp.co.cyberagent.android.gpuimage.sample.activity.GalleryActivity
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:33:9-62
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:33:19-59
activity#jp.co.cyberagent.android.gpuimage.sample.activity.CameraActivity
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:34:9-37:59
	android:screenOrientation
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:36:13-49
	android:theme
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:37:13-56
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:35:13-52
activity#jp.co.cyberagent.android.gpuimage.sample.activity.LightroomEditorActivity
ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:38:9-40:59
	android:theme
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:40:13-56
	android:name
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml:39:13-61
uses-sdk
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
MERGED from [:library] /Users/<USER>/Desktop/git/android-gpuimage/library/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:library] /Users/<USER>/Desktop/git/android-gpuimage/library/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.2.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/10b15c911195a22935b5370f26e9e44c/appcompat-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/10b15c911195a22935b5370f26e9e44c/appcompat-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/d64e89d8f607115f011738cc3440d5b7/fragment-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/d64e89d8f607115f011738cc3440d5b7/fragment-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/cd063c0dcf54faba3417c17936476171/jetified-appcompat-resources-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/cd063c0dcf54faba3417c17936476171/jetified-appcompat-resources-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/cb7eb146fe69c26b5c43254819d27294/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/cb7eb146fe69c26b5c43254819d27294/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/80ecf929044b86a5aa46915aa2d2281b/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/80ecf929044b86a5aa46915aa2d2281b/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8f3b96250de423d5be9b5ca48356f5fb/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8f3b96250de423d5be9b5ca48356f5fb/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8ee76296a2a1da1e044b91eff7400b45/jetified-activity-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/8ee76296a2a1da1e044b91eff7400b45/jetified-activity-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/b030ea72b339535608db7596e5689c4b/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/b030ea72b339535608db7596e5689c4b/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/e0109c0ed32efcd5b74be2da13868ff0/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/e0109c0ed32efcd5b74be2da13868ff0/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/705870a69888600444d749c96bebe43d/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/705870a69888600444d749c96bebe43d/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.3.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/22e35acf320ba1436f8703b3b8ba64f7/core-1.3.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.3.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/22e35acf320ba1436f8703b3b8ba64f7/core-1.3.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/71fab9291d2a8d398a1f9cad8ad8949f/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/71fab9291d2a8d398a1f9cad8ad8949f/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/efd12a819431f8eb8701e58f73a88386/versionedparcelable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/efd12a819431f8eb8701e58f73a88386/versionedparcelable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/e6fe80f301a425e9379179f5944495ef/lifecycle-runtime-2.1.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/e6fe80f301a425e9379179f5944495ef/lifecycle-runtime-2.1.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/ccd8403c623e9f66e3369083a163e0c2/lifecycle-viewmodel-2.1.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/ccd8403c623e9f66e3369083a163e0c2/lifecycle-viewmodel-2.1.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/b39b0582f616dfbe0cfd92a4661830cb/jetified-savedstate-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/b39b0582f616dfbe0cfd92a4661830cb/jetified-savedstate-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/58fd9af7829cf82f3521079d8c90d4a8/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/58fd9af7829cf82f3521079d8c90d4a8/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/44e6af7b037a2017d10299299b85d40f/lifecycle-livedata-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/44e6af7b037a2017d10299299b85d40f/lifecycle-livedata-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/b71fb64951ec116280d0ca535ff82573/lifecycle-livedata-core-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/b71fb64951ec116280d0ca535ff82573/lifecycle-livedata-core-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/1358fab37b79a0d95eb63d7bd7398e8e/core-runtime-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.0.0] /Users/<USER>/.gradle/caches/transforms-2/files-2.1/1358fab37b79a0d95eb63d7bd7398e8e/core-runtime-2.0.0/AndroidManifest.xml:20:5-44
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/Desktop/git/android-gpuimage/sample/src/main/AndroidManifest.xml
