<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/git/android-gpuimage/library/src/main/res"/><source path="/Users/<USER>/Desktop/git/android-gpuimage/library/build/generated/res/rs/debug"/><source path="/Users/<USER>/Desktop/git/android-gpuimage/library/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/git/android-gpuimage/library/src/main/res"><file path="/Users/<USER>/Desktop/git/android-gpuimage/library/src/main/res/values/attrs.xml" qualifiers=""><declare-styleable name="GPUImageView">
        <attr format="enum" name="gpuimage_surface_type">
            <enum name="surface_view" value="0"/>
            <enum name="texture_view" value="1"/>
        </attr>
        <attr format="boolean" name="gpuimage_show_loading"/>
    </declare-styleable></file></source><source path="/Users/<USER>/Desktop/git/android-gpuimage/library/build/generated/res/rs/debug"/><source path="/Users/<USER>/Desktop/git/android-gpuimage/library/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/git/android-gpuimage/library/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/git/android-gpuimage/library/src/debug/res"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="GPUImageView">
        <attr format="enum" name="gpuimage_surface_type">
            <enum name="surface_view" value="0"/>
            <enum name="texture_view" value="1"/>
        </attr>
        <attr format="boolean" name="gpuimage_show_loading"/>
    </declare-styleable></configuration></mergedItems></merger>